﻿using Hangfire;
using Multiempresa.Shared.Constants;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Extension;
using Multiempresa.Shared.Helpers.Convertores;
using Multiempresa.Shared.ViewModels.ImportacaoViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.Helpers;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services
{
    public class ImportacaoService : BaseService, IImportacaoService
    {
        private readonly IImportacaoRepository _importacaoRepository;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IStorageService _storageService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IPadronizacaoService _padronizacaoService;

        public ImportacaoService(INotificador notificador,
                          IImportacaoRepository importacaoRepository,
                          IStorageService storageService,
                          ILogAuditoriaService logAuditoriaService,
                          IAspNetUserInfo aspNetUserInfo,
                          IPadronizacaoService padronizacaoService) : base(notificador)
        {
            _importacaoRepository = importacaoRepository;
            _logAuditoriaService = logAuditoriaService;
            _storageService = storageService;
            _aspNetUserInfo = aspNetUserInfo;
            _padronizacaoService = padronizacaoService;
        }

        public async Task<Guid?> Cadastrar(ImportacaoViewModel importacaoViewModel)
        {
            if (await ExisteImportacaoProcessandoMesmoTipo(importacaoViewModel.Tipo))
            {
                NotificarAviso(string.Format(
                    ResourceMensagem.ImportacaoService_ExisteImportacaoEmAndamento,
                    importacaoViewModel.Tipo.ObterTipo()));
                return Guid.Empty;
            }

            var importacao = new Importacao
            {
                DataHora = DateTime.UtcNow,
                NomeArquivo = importacaoViewModel.NomeArquivo,
                Status = importacaoViewModel.Status,
                NomeArquivoStorage = string.Empty,
                Tipo = importacaoViewModel.Tipo,
                LojaId = _aspNetUserInfo.LojaId.Value
            };

            await _importacaoRepository.Insert(importacao);

            // Gera o caminho para o storage
            var nomeArquivo = GerarNomeArquivo(importacao.Tipo, importacao.Id, importacao.NomeArquivo);

            // Faz o upload do arquivo no storage
            await _storageService.Upload(StorageContaArmazenamento.Importacao, TipoArquivo.OUTROS, nomeArquivo, importacaoViewModel.FileBase64);

            // Preenche a entidade relacionada com as colunas configuradas
            importacao.NomeArquivoStorage = nomeArquivo.Split('/').LastOrDefault();
            importacao.ImportacaoCombinacaoCampos = importacaoViewModel.CombinacaoCampos == null ?
                                                                    null : importacaoViewModel.CombinacaoCampos
                                                                    .Where(c => !c.Ignorar)
                                                                    .Select(x => new ImportacaoCombinacaoCampos
                                                                    {
                                                                        ImportacaoId = importacao.Id,
                                                                        NumeroColunaPlanilha = x.NumeroColunaPlanilha,
                                                                        NumeroColunaSistema = x.NumeroColunaSistema
                                                                    }).ToList();

            await _importacaoRepository.SaveChanges();

            await _logAuditoriaService
                .Inserir(new LogAuditoriaInserirViewModel(BuscarTipoLog(importacao.Tipo), LogAuditoriaOperacao.CADASTRAR, $"Descrição: {importacao.NomeArquivo}"));

            // Agenda a tarefa no Hangfire
            BackgroundJob.Enqueue<Multiempresa.Shared.Interfaces.Hangfire.IImportacaoService>(x => x.Processar(
                importacao.Id,
                _aspNetUserInfo.CodigoContaEmpresa));

            return importacao.Id;
        }

        private async Task<bool> ExisteImportacaoProcessandoMesmoTipo(
            TipoImportacao tipo)
        {
            return await _importacaoRepository.Any(
            i => i.Tipo == tipo &&
                (i.Status == StatusImportacao.AGUARDANDO ||
                i.Status == StatusImportacao.EM_PROCESSAMENTO));
        }

        public async Task Alterar(ImportacaoViewModel importacaoViewModel)
        {
            var importacaoAtualizada = await _importacaoRepository.FindByKey(importacaoViewModel.Id);

            if (importacaoAtualizada == null)
            {
                NotificarAviso(ResourceMensagem.ImportacaoService_NaoEncontrada);
                return;
            }

            // Exclui o arquivo atual
            await _storageService.Excluir(StorageContaArmazenamento.Importacao,
                                          GerarNomeArquivo(importacaoAtualizada.Tipo, importacaoAtualizada.Id, importacaoAtualizada.NomeArquivoStorage));

            // Sobe o novo arquivo
            var nomeArquivo = GerarNomeArquivo(importacaoAtualizada.Tipo, importacaoAtualizada.Id, importacaoViewModel.NomeArquivo);
            await _storageService.Upload(StorageContaArmazenamento.Importacao, TipoArquivo.OUTROS, nomeArquivo, importacaoViewModel.FileBase64);

            importacaoAtualizada.Status = StatusImportacao.AGUARDANDO;
            importacaoAtualizada.NomeArquivoStorage = nomeArquivo.Split("/").LastOrDefault();

            var logAuditoria = new LogAuditoriaInserirViewModel(BuscarTipoLog(importacaoAtualizada.Tipo), LogAuditoriaOperacao.ALTERAR,
                Formatter.LogAuditoriaObservacaoPadrao(importacaoAtualizada.NomeArquivo, importacaoAtualizada.NomeArquivo, nameof(importacaoAtualizada.NomeArquivo)));

            await _importacaoRepository.SaveChanges();
            await _logAuditoriaService.Inserir(logAuditoria);

            // Agenda a tarefa no Hangfire
            BackgroundJob.Enqueue<Multiempresa.Shared.Interfaces.Hangfire.IImportacaoService>(x => x.Processar(
                importacaoAtualizada.Id,
                _aspNetUserInfo.CodigoContaEmpresa));
        }

        public GridPaginadaRetorno<ImportacaoPaginadaViewModel> ListarPaginado(GridPaginadaConsulta gridPaginada, string descricao, TipoImportacao tipoImportacao)
        {
            return _importacaoRepository.ListarPaginado(gridPaginada, descricao, tipoImportacao);
        }

        public async Task<string> Download(Guid id)
        {
            // Busca entidade pelo Guid ID
            var importacaoAtualizada = await _importacaoRepository.FindByKey(id);

            if (importacaoAtualizada != null)
            {
                // Gera o caminho para o storage
                var nomeArquivo = GerarNomeArquivo(importacaoAtualizada.Tipo, importacaoAtualizada.Id, importacaoAtualizada.NomeArquivoStorage);

                // Retorna o link do arquivo
                return _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Importacao, nomeArquivo);
            }

            return string.Empty;
        }

        public Task<string> DownloadCsvExemplo(TipoImportacao tipoImportacao)
        {
            var csvData = string.Empty;
            var separator = ';';

            foreach (var item in BuscarColunasSistema(tipoImportacao))
            {
                // Add o DisplayName da propriedade com o separador ';'
                csvData += $"{item.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName}{separator}";
            }

            // Remove o ultimo caractere
            csvData = csvData.Remove(csvData.Length - 1);

            return Task.FromResult(csvData);
        }

        public List<ViewModels.ImportacaoCombinacaoCamposViewModel> ListarColunas(ImportacaoViewModel importacaoViewModel)
        {
            var retorno = new List<ViewModels.ImportacaoCombinacaoCamposViewModel>();
            var colunas = BuscarColunasSistema(importacaoViewModel.Tipo);

            // Se o arquivo for Excel, converter para CSV
            var base64 = ConverterArquivo.ConverterXls(importacaoViewModel.FileBase64, somentePrimeiraLinha: true);
            var bytes = Convert.FromBase64String(base64);

            using (var sr = new StreamReader(new MemoryStream(bytes), Encoding.UTF8))
            {
                sr.BaseStream.Position = 0;

                if (!sr.EndOfStream)
                {
                    try
                    {
                        // Pega o header
                        var header = sr.ReadLine();
                        var valuesHeader = header.Split(';');

                        if (!sr.EndOfStream)
                        {
                            // Pega a próxima linha
                            var linha = sr.ReadLine();

                            // Divide as colunas
                            var valuesLinha = linha.Split(';');

                            // Preenche o retorno com todas colunas no header
                            retorno = valuesHeader
                                .GroupJoin(colunas, xl => xl.ToUpper(), vm => vm.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName.ToUpper(),
                                      (xlsx, viewModel) => new { viewModel, xlsx })
                                .Select((x, index) =>
                                {
                                    var ret = new ViewModels.ImportacaoCombinacaoCamposViewModel
                                    {
                                        Id = Guid.NewGuid(),
                                        NumeroColunaPlanilha = index,
                                        NomeColunaPlanilha = x.xlsx.Replace("\"", ""),
                                        Exemplo = x.viewModel.FirstOrDefault()?.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName.ToUpper() == valuesHeader[index].ToUpper() ? valuesLinha[index] : string.Empty,
                                        NumeroColunaSistema = colunas.FindIndex(s => s == x.viewModel.FirstOrDefault()),
                                        NomeColunaSistema = x.viewModel.FirstOrDefault()?.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName,
                                        Ignorar = false
                                    };

                                    return ret;
                                }).ToList();

                            // Se a quantidade de colunas no arquivo forem menores que a quantidade de propriedades
                            // presentes no ViewModel, o retorno será completado com as propriedades restantes
                            int countColum = 0;

                            if (retorno.Count < colunas.Count)
                                retorno.AddRange(
                                    colunas
                                    .Where(z =>
                                        !retorno.Select(x => x.NomeColunaSistema)
                                                .Any(x => x == z.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName))
                                    .Select((c, index) => new ViewModels.ImportacaoCombinacaoCamposViewModel
                                    {
                                        Id = Guid.NewGuid(),
                                        NumeroColunaSistema = Enumerable.Range(0, colunas.Count).Except(retorno.Select(c => c.NumeroColunaSistema)).ToArray()[countColum++],
                                        NomeColunaSistema = c.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName,
                                        Ignorar = true
                                    }).ToList());
                        }
                    }
                    catch { }
                }
            }

            // Coloca ignorar para as colunas que estiverem com diferênca entre os nomes da planilha e ViewModel
            if (retorno != null && retorno.Count > 0)
            {
                foreach (var item in retorno.Where(c => string.IsNullOrEmpty(c.NomeColunaPlanilha) || string.IsNullOrEmpty(c.NomeColunaSistema) || c.NomeColunaPlanilha.ToUpper() != c.NomeColunaSistema?.ToUpper()))
                {
                    item.Ignorar = true;
                }
            }

            return retorno;
        }

        public List<object> ListarColunasImportacao(TipoImportacao tipoImportacao)
        {
            var colunas = BuscarColunasSistema(tipoImportacao);

            var retorno = new List<object>();

            var count = 0;
            foreach (var c in colunas)
            {
                retorno.Add(new
                {
                    NomeColunaSistema = c.GetCustomAttributes<DisplayNameAttribute>(false).FirstOrDefault()?.DisplayName,
                    NumeroColunaSistema = count,
                });

                count++;
            }

            return retorno;
        }

        private List<PropertyInfo> BuscarColunasSistema(TipoImportacao tipoImportacao)
        {
            switch (tipoImportacao)
            {

                case TipoImportacao.Clientes:
                case TipoImportacao.Fornecedores:
                    return typeof(ImportacaoClienteFornecedorViewModel)
                            .GetProperties()
                            .ToList();
                case TipoImportacao.Produtos:
                    bool manterColunaCodigoIntegracao = _aspNetUserInfo.PossuiServico(ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA) ||
                                                        _aspNetUserInfo.PossuiServico(ReferenciaServicoStargate.MODULO_FRENTE_CAIXA) ||
                                                        _aspNetUserInfo.PossuiServico(ReferenciaServicoStargate.DISPOSITIVO_PDV);
                    string nomeColunaParaRemover = manterColunaCodigoIntegracao ? "" : "CodigoIntegracao";

                    return typeof(ImportacaoProdutoViewModel)
                             .GetProperties()
                             .Where(p => !nomeColunaParaRemover.Contains(p.Name))
                             .ToList();
                case TipoImportacao.ContasReceber:
                    return typeof(ImportacaoContasReceberViewModel)
                             .GetProperties()
                             .ToList();
                default:
                    return new List<PropertyInfo>();
            }
        }

        private string GerarNomeArquivo(TipoImportacao tipoImportacao, Guid id, string nomeArquivo)
        {
            return string.Format(CaminhoArquivosStorage.CaminhoImportacao,
                                 tipoImportacao.ToString().ToLower(),
                                 $"{id}.{nomeArquivo.Split('.').LastOrDefault()}");
        }

        private LogAuditoriaTela BuscarTipoLog(TipoImportacao tipoImportacao)
        {
            switch (tipoImportacao)
            {
                case TipoImportacao.Clientes:
                    return LogAuditoriaTela.IMPORTAR_CLIENTE;
                case TipoImportacao.Fornecedores:
                    return LogAuditoriaTela.IMPORTAR_FORNECEDOR;
                case TipoImportacao.Produtos:
                    return LogAuditoriaTela.IMPORTAR_PRODUTO;
                case TipoImportacao.ContasReceber:
                    return LogAuditoriaTela.IMPORTAR_CONTASRECEBER;
                default:
                    return LogAuditoriaTela.IMPORTAR_CLIENTE;
            }
        }

        public void Dispose()
        {
            _importacaoRepository?.Dispose();
        }
    }
}
