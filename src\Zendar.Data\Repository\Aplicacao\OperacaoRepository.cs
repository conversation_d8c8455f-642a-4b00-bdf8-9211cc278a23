﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.Entregadores;
using Zendar.Data.ViewModels;

namespace Zendar.Data.Repository.Aplicacao
{
    public class OperacaoRepository : RepositoryAplicacao<Operacao>, IOperacaoRepository
    {
        public OperacaoRepository(AplicacaoContexto context) : base(context)
        {
        }

        public Task Insert(Operacao operacao)
        {
            operacao.NumeroOperacao = 0;

            return base.Insert(operacao);
        }

        public long ObterUltimoNumeroInseridoPorLoja(Guid lojaId)
        {
            if (DbSet.Where(o => o.LojaId == lojaId).Any())
            {
                return DbSet.Where(o => o.LojaId == lojaId).Max(c => c.NumeroOperacao);
            }

            return 0;
        }

        public async Task<Operacao> ObterParaAlteracao(Guid id)
        {
            return await DbSet.Where(o => o.Id == id)
                              .Include(o => o.TipoOperacao)
                              .Include(o => o.Loja)
                                .ThenInclude(l => l.LojaFiscal)
                              .Include(o => o.DocumentoFiscal).ThenInclude(o => o.Loja)
                              .Include(o => o.OperacaoItens)
                              .Include(o => o.EntradaMercadoria)
                              .Include(o => o.MovimentacoesFinanceiras).ThenInclude(o => o.MovimentacoesFinanceirasBaixa)
                              .Include(o => o.Vale)
                              .FirstOrDefaultAsync();
        }

        public async Task<Operacao> ObterParaCancelar(Guid id)
        {
            return await DbSet.Where(o => o.Id == id)
                              .Include(o => o.TipoOperacao)
                              .Include(o => o.Loja)
                                .ThenInclude(l => l.LojaFiscal)
                              .Include(o => o.Loja)
                                .ThenInclude(o => o.LojaServicos)
                              .Include(o => o.DocumentoFiscal).ThenInclude(o => o.Loja)
                              .Include(o => o.OperacaoItens)
                                .ThenInclude(i => i.ProdutoCorTamanho)
                                    .ThenInclude(pct => pct.ProdutoCor)
                                        .ThenInclude(pc => pc.Produto)
                                            .ThenInclude(p => p.UnidadeMedida)
                              .Include(o => o.EntradaMercadoria)
                              .Include(o => o.MovimentacoesFinanceiras).ThenInclude(o => o.MovimentacoesFinanceirasBaixa)
                              .Include(o => o.CaixaMovimentacao)
                              .Include(o => o.Vale)
                              .FirstOrDefaultAsync();
        }

        public async Task<Operacao> ObterParaAlterarTabelaPreco(Guid operacaoId)
        {
            return await DbSet.Where(o => o.Id == operacaoId)
                                .Include(x => x.TipoOperacao)
                                .Include(x => x.OperacaoItens).ThenInclude(x => x.ProdutoCorTamanho).ThenInclude(x => x.ProdutoCor).ThenInclude(x => x.Produto)
                              .FirstOrDefaultAsync();
        }

        public async Task<Operacao> ObterParaAlteracaoComLocalEstoque(Guid id)
        {
            return await DbSet.Where(o => o.Id == id)
                              .Include(o => o.TipoOperacao)
                              .Include(o => o.DocumentoFiscal)
                              .Include(o => o.LocalEstoque)
                              .FirstOrDefaultAsync();
        }

        public async Task<Operacao> ObterParaAlteracaoComLocalEstoqueItens(Guid id)
        {
            return await DbSet.Where(o => o.Id == id)
                              .Include(o => o.TipoOperacao)
                              .Include(o => o.DocumentoFiscal)
                              .Include(o => o.LocalEstoque)
                              .Include(o => o.OperacaoItens)
                              .FirstOrDefaultAsync();
        }

        public Task<decimal> ObterValorTotalAcrescimo(Guid id)
        {
            return DbSet.Where(o => o.Id == id)
                        .Select(o => o.ValorTotalAcrescimo)
                        .FirstOrDefaultAsync();
        }

        public Task<Operacao> ObterComPagamentosParaAlteracao(Guid id)
        {
            return DbSet.Where(o => o.Id == id)
                        .Include(o => o.MovimentacoesFinanceiras)
                        .Include(o => o.TipoOperacao)
                        .FirstOrDefaultAsync();
        }

        public Task<Operacao> ObterComItensParaAlteracao(Guid id)
        {
            return DbSet.Where(o => o.Id == id)
                        .Include(o => o.OperacaoItens)
                        .FirstOrDefaultAsync();
        }

        public Task<TipoAcao> ObterAcaoEstoqueDoTipoOperacao(Guid id)
        {
            return DbSet.Where(o => o.Id == id).Select(o => o.TipoOperacao.AcaoEstoque).FirstOrDefaultAsync();
        }

        public async Task<Operacao> ObterOperacaoInformacoesNotaFiscal(Guid id)
        {
            return await DbSet.Where(x => x.Id.Equals(id)).Select(x => new Operacao
            {
                Id = x.Id,
                ClienteFornecedorId = x.ClienteFornecedorId,
                ClienteFornecedor = x.ClienteFornecedorId.HasValue
                                        ? new ClienteFornecedor
                                        {
                                            IndicadorIe = x.ClienteFornecedor.IndicadorIe,
                                            Enderecos = x.ClienteFornecedor.Enderecos.Where(e => e.Principal).Select(e => new Models.Aplicacao.Endereco
                                            {
                                                Cidade = new Cidade
                                                {
                                                    Estado = new Estado
                                                    {
                                                        Codigo = e.Cidade.Estado.Codigo,
                                                        Pais = e.Cidade.Estado.PaisId.HasValue
                                                                    ? new Pais { Id = e.Cidade.Estado.Pais.Id }
                                                                    : null
                                                    }
                                                }
                                            }).ToList()
                                        }
                                        : null,
                Loja = new Loja
                {
                    Id = x.Loja.Id,
                    Cidade = new Cidade
                    {
                        Estado = new Estado
                        {
                            Codigo = x.Loja.Cidade.Estado.Codigo,
                            Pais = x.Loja.Cidade.Estado.PaisId.HasValue
                                     ? new Pais { Id = x.Loja.Cidade.Estado.Pais.Id }
                                     : null
                        }
                    },
                    LojaFiscal = new LojaFiscal
                    {
                        RegimeTributario = x.Loja.LojaFiscal.RegimeTributario,
                        RegraFiscalPadrao = new RegraFiscal
                        {
                            Nome = x.Loja.LojaFiscal.RegraFiscalPadrao.Nome,
                            RegraFiscalItens = x.Loja.LojaFiscal.RegraFiscalPadrao.RegraFiscalItens
                                                    .Where(y => y.TipoOperacaoId.Equals(x.TipoOperacaoId)
                                                             && y.RegimeTributario.Equals(x.Loja.LojaFiscal.RegimeTributario))
                                                    .Select(t => new RegraFiscalItem
                                                    {
                                                        Id = t.Id,
                                                        IndicadorIe = t.IndicadorIe,
                                                        RegraFiscal = new RegraFiscal { Nome = t.RegraFiscal.Nome },
                                                        TipoOperacao = new TipoOperacao { Nome = t.TipoOperacao.Nome },
                                                        CfopDentroEstado = t.CfopDentroEstado,
                                                        CfopForaEstado = t.CfopForaEstado,
                                                        CfopForaPais = t.CfopForaPais,
                                                        CstCsosnDentroEstado = t.CstCsosnDentroEstado,
                                                        CstCsosnForaPais = t.CstCsosnForaPais,
                                                        PisCofinsCst = t.PisCofinsCst,
                                                        NaturezaOperacao = t.NaturezaOperacao,
                                                        RegrasFiscaisItemBaseLegal = t.RegrasFiscaisItemBaseLegal.Select(b => new RegraFiscalItemBaseLegal
                                                        {
                                                            BaseLegal = new BaseLegal
                                                            {
                                                                Id = b.BaseLegal.Id,
                                                                Descricao = b.BaseLegal.Descricao
                                                            }
                                                        }).ToList()
                                                    }).ToList()
                        }
                    }
                },
                ValorTotal = x.ValorTotal,
                ValorTotalItensSemDesconto = x.ValorTotalItensSemDesconto,
                TipoOperacaoId = x.TipoOperacaoId,
                TipoOperacao = new TipoOperacao
                {
                    Nome = x.TipoOperacao.Nome,
                    IdentificacaoTipoOperacao = x.TipoOperacao.IdentificacaoTipoOperacao
                },
                OperacaoItens = x.OperacaoItens,
                OperacaoTransferenciaOrigem = x.OperacaoTransferenciaOrigem != null
                ? new OperacaoTransferencia
                {
                    Id = x.OperacaoTransferenciaOrigem.Id
                }
                : null
            }).FirstOrDefaultAsync();
        }

        public async Task<Operacao> ObterOperacaoItens(Guid id)
        {
            return await DbSet.Where(o => o.Id == id)
                              .Include(o => o.OperacaoItens)
                              .AsNoTracking()
                              .Select(x => new Operacao
                              {
                                  LojaId = x.LojaId,
                                  OperacaoItens = x.OperacaoItens
                              })
                              .FirstOrDefaultAsync();
        }

        public async Task<Guid?> ObterCaixaAbertoOperacao(Guid id)
        {
            var operacao = await DbSet.Where(o => o.Id == id && !o.CaixaMovimentacao.DataHoraFechamento.HasValue)
                              .Select(x => new Operacao
                              {
                                  Id = id
                              })
                              .FirstOrDefaultAsync();

            return operacao == null ? null : operacao.Id;
        }

        public async Task<Operacao> ObterLancamentoFinanceiroCompleto(Guid id)
        {
            return await DbSet.Where(o => o.Id.Equals(id))
                              .Include(o => o.ClienteFornecedor)
                              .Include(o => o.MovimentacoesFinanceiras).ThenInclude(o => o.FormaPagamentoRecebimento)
                              .Include(o => o.MovimentacoesFinanceiras).ThenInclude(o => o.PlanoConta)
                              .Include(o => o.MovimentacoesFinanceiras).ThenInclude(o => o.MovimentacoesFinanceirasBaixa).ThenInclude(o => o.ContaFinanceira)
                              .FirstOrDefaultAsync();
        }

        public async Task<Guid?> ObterOperacaoCaixaUsuarioLogado(Guid idOperacao, Guid idUsuario)
        {
            // Busca a operaçõa pelo caixa aberto pelo usuario logado, ou se o tipo da conta financeira do caixa é conta cofre
            var operacao = await DbSet.FirstOrDefaultAsync(o => ((o.CaixaMovimentacao.UsuarioAberturaId.Equals(idUsuario)
                                                                && !o.CaixaMovimentacao.DataHoraFechamento.HasValue)
                                                                    || o.CaixaMovimentacao.ContaFinanceira.TipoContaFinanceira == TipoContaFinanceira.CONTA_COFRE)
                                                                && o.Id.Equals(idOperacao));

            return operacao?.Id;
        }

        public async Task<Guid?> ObterUltimoPedidoEfetuadoPorLojaUsuario(Guid lojaId, Guid usuarioId)
        {
            return await DbSet.Where(p => p.LojaId == lojaId &&
                                          p.UsuarioId == usuarioId &&
                                         p.Status == StatusOperacao.EFETUADA &&
                                         p.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.PEDIDO)
                              .OrderByDescending(p => p.DataEmissao)
                              .Select(p => p.Id)
                              .FirstOrDefaultAsync();
        }

        public async Task<decimal> ObterSaldoAtualContaFinanceira(Guid contaId, DateTime dataLancamentoSaldo)
        {
            return await DbSet.Where(o => o.Status.Equals(StatusOperacao.EFETUADA)
                                    && o.MovimentacoesFinanceiras.Any(m => m.MovimentacoesFinanceirasBaixa.Any(mb => mb.ContaFinanceiraId.Equals(contaId)))
                                    && o.DataEmissao.Date <= dataLancamentoSaldo.Date)
                    .SumAsync(o => o.TipoOperacao.AcaoFinanceira.Equals(TipoAcao.ENTRADA) ? o.ValorTotal : -o.ValorTotal);
        }

        public async Task<Operacao> ObterInformacoesVendaParaTroca(Guid operacaoId)
        {
            return await DbSet.Where(o => o.Id.Equals(operacaoId))
                              .Select(o => new Operacao
                              {
                                  NumeroOperacao = o.NumeroOperacao,
                                  CaixaMovimentacaoId = o.CaixaMovimentacaoId,
                                  CaixaMovimentacao = new CaixaMovimentacao
                                  {
                                      ContaFinanceira = new ContaFinanceira { Nome = o.CaixaMovimentacao.ContaFinanceira.Nome }
                                  },
                                  Vendedor = new Vendedor
                                  {
                                      Nome = o.Vendedor.Nome,
                                  },
                                  TabelaPreco = new TabelaPreco
                                  {
                                      Nome = o.TabelaPreco.Nome,
                                  },
                              })
                              .FirstOrDefaultAsync();
        }
        public decimal ObterValorTotalVendasPeriodo(DateTime dataInicio, DateTime dataFim, Guid lojaId)
        {
            var queryWhere = DbSet.Where(x => x.DataEmissao >= dataInicio &&
                                              x.DataEmissao <= dataFim &&
                                              x.LojaId == lojaId);

            queryWhere = queryWhere.Where(x => (x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ||
                                                x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO) &&
                                                x.Status == StatusOperacao.EFETUADA);

            var valorTotalPeriodo = queryWhere.Sum(x => x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ? x.ValorTotal : -x.ValorTotal);

            return valorTotalPeriodo;
        }
        public IQueryable<Operacao> FiltrarVendas(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            var queryWhere = DbSet.Where(x => x.DataEmissao >= filtrosViewModel.DataEmissaoInicio &&
                                              x.DataEmissao <= filtrosViewModel.DataEmissaoFim &&
                                              x.LojaId == lojaId);

            if (filtrosViewModel.ClienteFornecedorId.HasValue)
                queryWhere = queryWhere.Where(x => x.ClienteFornecedorId == filtrosViewModel.ClienteFornecedorId);

            if (filtrosViewModel.VendedorId.HasValue)
                queryWhere = queryWhere.Where(x => x.VendedorId == filtrosViewModel.VendedorId);

            if (filtrosViewModel.EntregadorId.HasValue)
                queryWhere = queryWhere.Where(x => x.EntregadorId == filtrosViewModel.EntregadorId);

            if (filtrosViewModel.StatusConsulta.HasValue)
                queryWhere = queryWhere.Where(x => x.Status == filtrosViewModel.StatusConsulta);
            
            if (filtrosViewModel.LocalEstoqueId.HasValue)
                queryWhere = queryWhere.Where(x => x.LocalEstoqueId == filtrosViewModel.LocalEstoqueId);
            
            if (filtrosViewModel.Origem.HasValue && filtrosViewModel.Origem.Value != IdentificacaoIntegracao.TODAS)
            {
                if (filtrosViewModel.Origem.Value != IdentificacaoIntegracao.CAIXA_MOVEL)
                {
                    queryWhere = queryWhere.Where(o => o.IntegracaoPedidos.Any(p => p.Integracao.IdentificacaoIntegracao == filtrosViewModel.Origem.Value));
                }
                else
                {
                    queryWhere = queryWhere.Where(o => o.IntegracaoPedidos.Any(p => p.Integracao.IdentificacaoIntegracao == filtrosViewModel.Origem.Value) ||
                                             (o.DispositivoId.HasValue && !o.IntegracaoPedidos.Any())); // devolucoes
                }
            }
            else if (!filtrosViewModel.Origem.HasValue)
            {
                queryWhere = queryWhere.Where(o => !o.IntegracaoPedidos.Any() && !o.DispositivoId.HasValue);
            }
            
            if (filtrosViewModel.TipoFiscal.Any() &&
                !filtrosViewModel.TipoFiscal.Contains(TipoFiscal.TODOS))
            {
                if (filtrosViewModel.TipoFiscal.Contains(TipoFiscal.SEM_FISCAL))
                {
                    queryWhere = queryWhere.Where(o => !o.DocumentoFiscal.Any());
                }
                else
                {
                    queryWhere = queryWhere.Where(o =>
                        o.DocumentoFiscal.Any(x =>
                            (filtrosViewModel.TipoFiscal.Contains(TipoFiscal.NFCE) &&
                                (x.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.NFCe ||
                                 x.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.SAT)) ||
                            (filtrosViewModel.TipoFiscal.Contains(TipoFiscal.NFE) &&
                                x.ModeloFiscal == ZendarPackage.NotaFiscal.Enums.ModeloFiscal.NFe)));
                }
            }

            queryWhere = queryWhere.Where(x => x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA || x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO);

            return queryWhere;
        }
        public async Task<List<Operacao>> ObterRelatoriosVendaRecebimento(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                              .Select(o => new Operacao
                              {
                                  MovimentacoesFinanceiras = o.MovimentacoesFinanceiras.Select(m => new MovimentacaoFinanceira
                                  {
                                      Valor = m.Valor,
                                      Operacao = new Operacao
                                      {
                                          Status = o.Status,
                                          TipoOperacao = new TipoOperacao
                                          {
                                              //Pegando o TipoOperacao da Operacao, e não da movimentacaoFinanceira
                                              IdentificacaoTipoOperacao = o.TipoOperacao.IdentificacaoTipoOperacao
                                          },
                                          DocumentoFiscal = o.DocumentoFiscal
                                      },
                                      FormaPagamentoRecebimentoId = m.FormaPagamentoRecebimentoId,
                                      FormaPagamentoRecebimento = new FormaPagamentoRecebimento
                                      {
                                          Nome = m.FormaPagamentoRecebimento.Nome
                                      }
                                  }).ToList()
                              })
                              .ToListAsync();
        }

        public async Task<List<Operacao>> ObterRelatoriosVendaDia(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {

            return await FiltrarVendas(filtrosViewModel, lojaId)
                              .Select(o => new Operacao
                              {
                                  ValorTotal = o.ValorTotal,
                                  OperacaoItens = o.OperacaoItens.Select(i => new OperacaoItem { Quantidade = i.Quantidade }).ToList(),
                                  TipoOperacao = new TipoOperacao { IdentificacaoTipoOperacao = o.TipoOperacao.IdentificacaoTipoOperacao },
                                  DataEmissao = o.DataEmissao,
                                  Status = o.Status,
                                  QuantidadePessoas = o.QuantidadePessoas,
                              })
                              .ToListAsync();
        }

        public async Task<List<Operacao>> ObterRelatoriosVendaEntregadorDetalhado(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {

            return await FiltrarVendas(filtrosViewModel, lojaId)
                              .Select(o => new Operacao
                              {
                                  Entregador = new Entregador { Nome = o.Entregador.Nome },
                                  NumeroOperacao = o.NumeroOperacao,
                                  ValorTotal = o.ValorTotal,
                                  ValorEntrega = o.ValorEntrega,
                                  ClienteFornecedor = new ClienteFornecedor { Codigo = o.ClienteFornecedor.Codigo, Nome = o.ClienteFornecedor.Nome }
                              })
                              .ToListAsync();
        }
        public async Task<List<Operacao>> ObterRelatoriosLucroPorDia(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                              .Select(o => new Operacao
                              {
                                  DataEmissao = o.DataEmissao,
                                  OperacaoItens = o.OperacaoItens.Select(i => new OperacaoItem
                                  {
                                      Operacao = new Operacao
                                      {
                                          TipoOperacao = o.TipoOperacao
                                      },
                                      PrecoCusto = i.PrecoCusto,
                                      DescontoDistribuido = i.DescontoDistribuido,
                                      ValorItemComDesconto = i.ValorItemComDesconto,
                                      AcrescimoDistribuido = i.AcrescimoDistribuido,
                                      FreteDistribuido = i.FreteDistribuido,
                                      OutrasDespesasDistribuido = i.OutrasDespesasDistribuido
                                  }).ToList()
                              })
                              .ToListAsync();
        }
        public async Task<Operacao> ObterOperacaoComItensEItensKit(Guid operacaoId)
        {
            return await DbSet.Where(o => o.Id.Equals(operacaoId))
                                .Include(o => o.OperacaoItens)
                                    .ThenInclude(i => i.OperacoesItensKit)
                              .FirstOrDefaultAsync();
        }

        public async Task<List<Operacao>> ObterItensConsignados(Guid clienteFornecedorId, Guid lojaId)
        {
            var query = DbSet.Where(o => o.ClienteFornecedorId.Equals(clienteFornecedorId) &&
                                         o.TipoOperacao.IdentificacaoTipoOperacao.Equals(IdentificacaoTipoOperacao.CONSIGNACAO) &&
                                         o.Status.Equals(StatusOperacao.EFETUADA) &&
                                         o.LojaId.Equals(lojaId) &&
                                         o.OperacaoItens.Any(i => i.Status.Equals(StatusOperacaoItem.EFETUADO)));

            return await query
                        .OrderBy(o => o.DataEmissao)
                        .Select(o => new Operacao
                        {
                            Id = o.Id,
                            NumeroOperacao = o.NumeroOperacao,
                            DataEmissao = o.DataEmissao,
                            Vendedor = new Vendedor { Nome = o.Vendedor.Nome },
                            TabelaPreco = new TabelaPreco { Nome = o.TabelaPreco.Nome },
                            CaixaMovimentacao = new CaixaMovimentacao
                            {
                                ContaFinanceira = new ContaFinanceira { Nome = o.CaixaMovimentacao.ContaFinanceira.Nome },
                            },
                            OperacaoItens = o.OperacaoItens
                                                    .OrderBy(x => x.ProdutoCorTamanho.ProdutoCor.Produto.Nome)
                                                    .ThenBy(x => x.ProdutoCorTamanho.ProdutoCor.Cor.Descricao)
                                                    .ThenBy(x => x.ProdutoCorTamanho.Tamanho.SequenciaOrdenacao)
                                                    .Where(i => i.Status.Equals(StatusOperacaoItem.EFETUADO) &&
                                                                       i.OperacaoItemKitId == null)
                                                            .Select(i => new OperacaoItem
                                                            {
                                                                Id = i.Id,
                                                                ProdutoCorTamanhoId = i.ProdutoCorTamanhoId,
                                                                ValorUnitario = i.ValorUnitario,
                                                                ValorItemComDesconto = i.ValorItemComDesconto,
                                                                ValorItemSemDesconto = i.ValorItemSemDesconto,
                                                                Quantidade = i.Quantidade,
                                                                ProdutoCorTamanho = new ProdutoCorTamanho
                                                                {
                                                                    CodigoBarrasInterno = i.ProdutoCorTamanho.CodigoBarrasInterno,
                                                                    CodigoExterno = i.ProdutoCorTamanho.CodigoExterno,
                                                                    CodigoGTINEAN = i.ProdutoCorTamanho.CodigoGTINEAN,
                                                                    SequenciaCodigoBarras = i.ProdutoCorTamanho.SequenciaCodigoBarras,
                                                                    Tamanho = new Tamanho
                                                                    {
                                                                        Descricao = i.ProdutoCorTamanho.Tamanho.Descricao,
                                                                        PadraoSistema = i.ProdutoCorTamanho.Tamanho.PadraoSistema,
                                                                        SequenciaOrdenacao = i.ProdutoCorTamanho.Tamanho.SequenciaOrdenacao
                                                                    },
                                                                    ProdutoCor = new ProdutoCor
                                                                    {
                                                                        Cor = new Cor
                                                                        {
                                                                            Descricao = i.ProdutoCorTamanho.ProdutoCor.Cor.Descricao,
                                                                            PadraoSistema = i.ProdutoCorTamanho.ProdutoCor.Cor.PadraoSistema,
                                                                        },
                                                                        Produto = new Produto
                                                                        {
                                                                            Nome = i.ProdutoCorTamanho.PesquisaProduto.Nome,
                                                                            UnidadeMedida = new UnidadeMedida
                                                                            {
                                                                                VolumeUnitario = i.ProdutoCorTamanho.PesquisaProduto.Produto.UnidadeMedida.VolumeUnitario
                                                                            }
                                                                        }
                                                                    }
                                                                },
                                                            }).ToList()
                        }).ToListAsync();
        }

        public async Task<IdentificacaoTipoOperacao> ObterTipoOperacao(Guid operacaoId)
        {
            return await DbSet.Where(x => x.Id == operacaoId).Select(o => o.TipoOperacao.IdentificacaoTipoOperacao).FirstOrDefaultAsync();
        }

        public async Task<bool> VendaEmAberto(long numeroOperacao, Guid lojaId)
        {
            return await DbSet.AnyAsync(x => x.LojaId.Equals(lojaId) &&
                                             x.NumeroOperacao == numeroOperacao &&
                                             x.MovimentacoesFinanceiras.Any(m => m.Valor > m.MovimentacoesFinanceirasBaixa.Sum(x => x.Valor - x.Multa - x.Juros + x.Desconto)));
        }

        public IQueryable<Operacao> ObterParaHistoricoComissao(FiltroRelatoriosMetaComissaoViewModel filtros, Guid lojaId)
        {
            var query = DbSet.Where(x => x.LojaId == lojaId &&
                                         x.Status == StatusOperacao.EFETUADA &&
                                         x.DataEmissao.Month == filtros.MesAno.Month &&
                                         x.DataEmissao.Year == filtros.MesAno.Year &&
                                         x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA);

            if (filtros.VendedorId.HasValue)
                query = query.Where(x => x.VendedorId == filtros.VendedorId.Value);

            query = query.Select(x => new Operacao
            {
                Id = x.Id,
                DataEmissao = x.DataEmissao,
                NumeroOperacao = x.NumeroOperacao,
                ValorTotal = x.ValorTotal,
                ValorParaComissao = x.ValorParaComissao,
                ClienteFornecedor = new ClienteFornecedor { Nome = x.ClienteFornecedor.Nome },
                VendedorId = x.VendedorId,
                Vendedor = new Vendedor
                {
                    Nome = x.Vendedor.Nome,
                    Ativo = x.Vendedor.Ativo
                },
            }).OrderBy(x => x.DataEmissao);

            return query;
        }

        public async Task<List<Operacao>> ObterValorTrocaDevolucaoCaixa(Guid caixaMovimentacaoId)
        {
            return await DbSet.Where(x => x.CaixaMovimentacaoId == caixaMovimentacaoId &&
                                          x.Status == StatusOperacao.EFETUADA &&
                                          x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO)
                              .Select(x => new Operacao
                              {
                                  ValorTotal = x.ValorTotal,
                                  TipoOperacao = new TipoOperacao { IdentificacaoTipoOperacao = x.TipoOperacao.IdentificacaoTipoOperacao }
                              })
                              .ToListAsync();
        }

        public async Task<List<Operacao>> ObterParaCalcularValorParaComissao(List<Guid> operacaoId)
        {
            return await DbSet.Where(o => operacaoId.Contains(o.Id))
                              .Include(o => o.MovimentacoesFinanceiras)
                                .ThenInclude(m => m.MovimentacoesFinanceirasBaixa)
                              .ToListAsync();
        }

        public async Task<List<Operacao>> ObterOperacoesVendaParaCalcularComissao(Guid lojaId, DateTime periodo)
        {
            return await DbSet.Where(o => o.LojaId == lojaId &&
                                          o.Status == StatusOperacao.EFETUADA &&
                                          o.DataEmissao >= periodo &&
                                          o.TipoOperacao.IdentificacaoTipoOperacao.Equals(IdentificacaoTipoOperacao.VENDA))
                              .Select(o => new Operacao
                              {
                                  Id = o.Id,
                                  ValorParaComissao = o.ValorParaComissao,
                                  LojaId = o.LojaId,
                                  VendedorId = o.VendedorId,
                                  DataHoraUsuario = o.DataHoraUsuario,
                                  OperacaoItens = o.OperacaoItens.Where(i => i.Status == StatusOperacaoItem.EFETUADO && i.OperacaoItemKitId == null).Select(i => new OperacaoItem
                                  {
                                      Id = i.Id,
                                      ItemKit = i.ItemKit,
                                      Quantidade = i.Quantidade,
                                      OperacoesItensKit = i.OperacoesItensKit.Select(x => new OperacaoItem
                                      {
                                          Id = x.Id,
                                      }).ToList(),
                                  }).ToList(),
                              }).ToListAsync();
        }

        public async Task<List<Operacao>> ObterOperacoesDevolucaoParaCalcularComissao(Guid lojaId, DateTime periodo)
        {
            return await DbSet.Where(o => o.LojaId == lojaId &&
                                          o.Status == StatusOperacao.EFETUADA &&
                                          o.DataEmissao >= periodo &&
                                          o.TipoOperacao.IdentificacaoTipoOperacao.Equals(IdentificacaoTipoOperacao.DEVOLUCAO)
                                          )
                              .Select(o => new Operacao
                              {
                                  Id = o.Id,
                                  ValorParaComissao = o.ValorParaComissao,
                                  LojaId = o.LojaId,
                                  VendedorId = o.VendedorId,
                                  DataHoraUsuario = o.DataHoraUsuario,
                                  OperacaoItens = o.OperacaoItens.Where(i => i.Status == StatusOperacaoItem.EFETUADO && i.OperacaoItemKitId == null).Select(i => new OperacaoItem
                                  {
                                      Id = i.Id,
                                      ItemKit = i.ItemKit,
                                      Quantidade = i.Quantidade,
                                      OperacoesItensKit = i.OperacoesItensKit.Select(x => new OperacaoItem
                                      {
                                          Id = x.Id,
                                      }).ToList(),
                                  }).ToList(),
                              }).ToListAsync();
        }

        public async Task<Operacao> ObterParaEtiquetaDespacho(Guid operacaoId)
        {
            return await DbSet
                              .Where(o => o.Id == operacaoId)
                              .Select(o => new Operacao
                              {
                                  NumeroOperacao = o.NumeroOperacao,
                                  Loja = new Loja
                                  {
                                      Fantasia = o.Loja.Fantasia,
                                      Logradouro = o.Loja.Logradouro,
                                      Complemento = o.Loja.Complemento,
                                      Numero = o.Loja.Numero,
                                      Bairro = o.Loja.Bairro,
                                      Cep = o.Loja.Cep,
                                      Cidade = new Cidade { CidadeUf = o.Loja.Cidade.CidadeUf },
                                      LojaImpressaoRelatorio = new LojaImpressaoRelatorio { LogoQuadrado = o.Loja.LojaImpressaoRelatorio.LogoQuadrado }
                                  },
                                  ClienteFornecedor = new ClienteFornecedor
                                  {
                                      Nome = o.ClienteFornecedor.Nome,
                                      Apelido = o.ClienteFornecedor.Apelido,
                                      TipoPessoa = o.ClienteFornecedor.TipoPessoa,
                                      Enderecos = o.ClienteFornecedor.Enderecos.Where(e => e.Principal)
                                                                               .Select(e => new Models.Aplicacao.Endereco
                                                                               {
                                                                                   Logradouro = e.Logradouro,
                                                                                   Numero = e.Numero,
                                                                                   Bairro = e.Bairro,
                                                                                   Complemento = e.Complemento,
                                                                                   Cep = e.Cep,
                                                                                   Cidade = new Cidade { CidadeUf = e.Cidade.CidadeUf }
                                                                               })
                                                                               .ToList()
                                  }
                              })
                              .FirstOrDefaultAsync();
        }

        public async Task<List<Operacao>> ObterParaRecalcularVendasSTi3Dashboard(DateTime dataInicio, DateTime dataFim)
        {
            return await DbSet
                            .Where(x => x.DataEmissao >= dataInicio &&
                                        x.DataEmissao < dataFim &&
                                        x.Status == StatusOperacao.EFETUADA &&
                                        (x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA ||
                                         x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.DEVOLUCAO))
                            .Select(x => new Operacao
                            {
                                Id = x.Id,
                                LojaId = x.LojaId,
                                VendedorId = x.VendedorId,
                                Status = x.Status,
                                DataEmissao = x.DataEmissao,
                                DataHoraUsuario = x.DataHoraUsuario,
                                ValorTotal = x.ValorTotal,
                                ValorTotalItensSemDesconto = x.ValorTotalItensSemDesconto,
                                ValorTotalAcrescimo = x.ValorTotalAcrescimo,
                                ValorTotalFrete = x.ValorTotalFrete,
                                ValorTotalOutrasDespesas = x.ValorTotalOutrasDespesas,
                                ValorTotalDescontoItem = x.ValorTotalDescontoItem,
                                ValorTotalDescontoAdicional = x.ValorTotalDescontoAdicional,
                                TipoOperacao = new TipoOperacao
                                {
                                    IdentificacaoTipoOperacao = x.TipoOperacao.IdentificacaoTipoOperacao,
                                },
                                MovimentacoesFinanceiras = x.MovimentacoesFinanceiras.Select(y => new MovimentacaoFinanceira
                                {
                                    FormaPagamentoRecebimentoId = y.FormaPagamentoRecebimentoId,
                                    Valor = y.Valor
                                }).ToList(),
                                OperacaoItens = x.OperacaoItens.Where(x => !x.ItemKit).Select(y => new OperacaoItem
                                {
                                    Quantidade = y.Quantidade,
                                    ValorItemComDesconto = y.ValorItemComDesconto,
                                    ProdutoCorTamanho = new ProdutoCorTamanho
                                    {
                                        ProdutoCor = new ProdutoCor
                                        {
                                            Produto = new Produto
                                            {
                                                Id = y.ProdutoCorTamanho.ProdutoCor.Produto.Id,
                                                Nome = y.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                                                CategoriaProdutoId = y.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProdutoId,
                                                UnidadeMedida = new UnidadeMedida
                                                {
                                                    VolumeUnitario = y.ProdutoCorTamanho.ProdutoCor.Produto.UnidadeMedida.VolumeUnitario
                                                }
                                            }
                                        }
                                    }
                                }).ToList(),
                            })
                            .ToListAsync();
        }

        public async Task<Operacao> ObterComItensEPagamentos(Guid operacaoId)
        {
            return await DbSet
                            .AsNoTracking()
                            .Select(x => new Operacao
                            {
                                Id = x.Id,
                                OperacaoOrigemDevolucaoId = x.OperacaoOrigemDevolucaoId,
                                LojaId = x.LojaId,
                                VendedorId = x.VendedorId,
                                DataHoraUsuario = x.DataHoraUsuario,
                                ValorTotal = x.ValorTotal,
                                ValorTotalItensSemDesconto = x.ValorTotalItensSemDesconto,
                                ValorTotalAcrescimo = x.ValorTotalAcrescimo,
                                ValorTotalFrete = x.ValorTotalFrete,
                                ValorTotalOutrasDespesas = x.ValorTotalOutrasDespesas,
                                ValorTotalDescontoItem = x.ValorTotalDescontoItem,
                                ValorTotalDescontoAdicional = x.ValorTotalDescontoAdicional,
                                Status = x.Status,
                                TipoOperacao = new TipoOperacao { IdentificacaoTipoOperacao = x.TipoOperacao.IdentificacaoTipoOperacao },
                                MovimentacoesFinanceiras = x.MovimentacoesFinanceiras.Select(y => new MovimentacaoFinanceira
                                {
                                    FormaPagamentoRecebimentoId = y.FormaPagamentoRecebimentoId,
                                    Valor = y.Valor,
                                }).ToList(),
                                OperacaoItens = x.OperacaoItens.Where(x => !x.ItemKit).Select(y => new OperacaoItem
                                {
                                    Quantidade = y.Quantidade,
                                    ValorItemComDesconto = y.ValorItemComDesconto,
                                    ProdutoCorTamanho = new ProdutoCorTamanho
                                    {
                                        ProdutoCor = new ProdutoCor
                                        {
                                            Produto = new Produto
                                            {
                                                Id = y.ProdutoCorTamanho.ProdutoCor.Produto.Id,
                                                Nome = y.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                                                CategoriaProdutoId = y.ProdutoCorTamanho.ProdutoCor.Produto.CategoriaProdutoId,
                                                UnidadeMedida = new UnidadeMedida
                                                {
                                                    VolumeUnitario = y.ProdutoCorTamanho.ProdutoCor.Produto.UnidadeMedida.VolumeUnitario
                                                }
                                            }
                                        }
                                    }
                                }).ToList(),
                            })
                            .FirstOrDefaultAsync(x => x.Id == operacaoId);
        }

        public async Task<Operacao> ObterUltimaOperacaoVendaLoja(Guid lojaId)
        {
            return await DbSet
                            .Where(x => x.LojaId == lojaId &&
                                        x.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA)
                            .Select(x => new Operacao
                            {
                                LojaId = x.LojaId,
                                DataEmissao = x.DataEmissao,
                                DataHoraUsuario = x.DataHoraUsuario
                            })
                            .OrderByDescending(x => x.DataEmissao)
                            .FirstOrDefaultAsync();
        }

        public async Task<List<Operacao>> ObterRelatoriosVendaVendedor(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                            .Select(op => new Operacao
                            {
                                ValorTotal = op.ValorTotal,
                                ValorEntrega = op.ValorEntrega,
                                ValorTotalAcrescimo = op.ValorTotalAcrescimo,
                                Status = op.Status,
                                TipoOperacao = new TipoOperacao
                                {
                                    IdentificacaoTipoOperacao = op.TipoOperacao.IdentificacaoTipoOperacao,
                                },
                                IntegracaoPedidos = op.IntegracaoPedidos,
                                Dispositivo = op.Dispositivo,
                                VendedorId = op.VendedorId,
                                Vendedor = new Vendedor
                                {
                                    Nome = op.Vendedor.Nome,
                                },
                                OperacaoItens = op.OperacaoItens.Select(oi => new OperacaoItem
                                {
                                    Quantidade = oi.Quantidade,
                                    AcrescimoDistribuido = oi.AcrescimoDistribuido,
                                    FreteDistribuido = oi.FreteDistribuido,
                                    OutrasDespesasDistribuido = oi.OutrasDespesasDistribuido,
                                    DescontoDistribuido = oi.DescontoDistribuido,
                                    ValorDescontoItem = oi.ValorDescontoItem,
                                    ValorTaxaServico = oi.ValorTaxaServico,
                                    Operacao = new Operacao
                                    {
                                        ValorEntrega = oi.Operacao.ValorEntrega
                                    }

                                }).ToList(),
                            })
                            .ToListAsync();
        }

        public async Task<List<Operacao>> ObterRelatoriosVendaTotalizadasVendedor(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                            .Select(op => new Operacao
                            {
                                ValorTotal = op.ValorTotal,
                                Status = op.Status,
                                ValorTotalItensSemDesconto = op.ValorTotalItensSemDesconto,
                                ValorEntrega = op.ValorEntrega,
                                ValorTotalFrete = op.ValorTotalFrete,
                                ValorTotalOutrasDespesas = op.ValorTotalOutrasDespesas,
                                ValorTotalDescontoAdicional = op.ValorTotalDescontoAdicional,
                                ValorTotalDescontoItem = op.ValorTotalDescontoItem,
                                Dispositivo = op.Dispositivo,

                                TipoOperacao = new TipoOperacao
                                {
                                    IdentificacaoTipoOperacao = op.TipoOperacao.IdentificacaoTipoOperacao,
                                },
                                VendedorId = op.VendedorId,
                                Vendedor = new Vendedor
                                {
                                    Nome = op.Vendedor.Nome,
                                },
                                OperacaoItens = op.OperacaoItens.Select(oi => new OperacaoItem
                                {
                                    AcrescimoDistribuido = oi.AcrescimoDistribuido,
                                    OutrasDespesasDistribuido = oi.OutrasDespesasDistribuido,
                                    FreteDistribuido = oi.FreteDistribuido,
                                    ValorTaxaServico = oi.ValorTaxaServico

                                }).ToList(),
                            })
                            .ToListAsync();
        }

        public async Task<List<Operacao>> ObterRelatoriosVendaEntregadorResumo(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                            .Select(op => new Operacao
                            {
                                ValorTotal = op.ValorTotal,
                                ValorEntrega = op.ValorEntrega,
                                Status = op.Status,
                                TipoOperacao = new TipoOperacao
                                {
                                    IdentificacaoTipoOperacao = op.TipoOperacao.IdentificacaoTipoOperacao,
                                },
                                EntregadorId = op.EntregadorId,
                                Entregador = new Entregador
                                {
                                    Nome = op.Entregador.Nome,
                                },
                            })
                            .ToListAsync();
        }
        public async Task<List<Operacao>> ObterRelatoriosVendaTotalizadaPorProdutoSimples(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                            .Select(op => new Operacao
                            {
                                ValorTotal = op.ValorTotal,
                                TipoOperacao = new TipoOperacao
                                {
                                    IdentificacaoTipoOperacao = op.TipoOperacao.IdentificacaoTipoOperacao,
                                },
                                OperacaoItens = op.OperacaoItens.Select(oi => new OperacaoItem
                                {
                                    Quantidade = oi.Quantidade,
                                    ValorDescontoItem = oi.ValorDescontoItem,
                                    ValorItemComDesconto = oi.ValorItemComDesconto,
                                    AcrescimoDistribuido = oi.AcrescimoDistribuido,
                                    FreteDistribuido = oi.FreteDistribuido,
                                    DescontoDistribuido = oi.DescontoDistribuido,
                                    OutrasDespesasDistribuido = oi.OutrasDespesasDistribuido,
                                    Operacao = new Operacao
                                    {
                                        TipoOperacao = new TipoOperacao
                                        {
                                            IdentificacaoTipoOperacao = oi.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                                        },
                                        ValorTotal = op.ValorTotal,
                                    },
                                    ProdutoCorTamanho = new ProdutoCorTamanho
                                    {
                                        ProdutoCor = new ProdutoCor
                                        {
                                            Produto = new Produto
                                            {
                                                Nome = oi.ProdutoCorTamanho.ProdutoCor.Produto.Nome,
                                            }
                                        }
                                    }
                                }).ToList(),
                            })
                            .ToListAsync();
        }

        public async Task<Operacao> ObterPorIdentificadorPedido(string identificadorPedido)
        {
            return await DbSet
                            .Where(x => x.IdentificadorPedido == identificadorPedido)
                            .Select(x => x)
                            .FirstOrDefaultAsync();
        }

        public async Task<List<Operacao>> ObterRelatoriosVendaValoresAdicionais(RelatorioVendaFiltrosViewModel filtrosViewModel, Guid lojaId)
        {
            return await FiltrarVendas(filtrosViewModel, lojaId)
                        .Select(o => new Operacao
                        {
                            NumeroOperacao = o.NumeroOperacao,
                            ClienteFornecedor = new ClienteFornecedor { Codigo = o.ClienteFornecedor.Codigo, Nome = o.ClienteFornecedor.Nome },
                            DataEmissao = o.DataEmissao,
                            DocumentoFiscal = o.DocumentoFiscal.Select(d => new DocumentoFiscal { ModeloFiscal = d.ModeloFiscal, Numero = d.Numero }).ToList(),
                            CupomSat = o.CupomSat.Select(d => new CupomSat { NumeroCupom = d.NumeroCupom }).ToList(),
                            ValorTotalItensSemDesconto = o.ValorTotalItensSemDesconto,
                            ValorTotalAcrescimo = o.ValorTotalAcrescimo,
                            ValorTotalFrete = o.ValorTotalFrete,
                            ValorTotalOutrasDespesas = o.ValorTotalOutrasDespesas,
                            ValorTotalDescontoAdicional = o.ValorTotalDescontoAdicional,
                            ValorTotalDescontoItem = o.ValorTotalDescontoItem,
                            ValorCouvert = o.ValorCouvert,
                            ValorEntrega = o.ValorEntrega,
                            ValorTotal = o.ValorTotal,
                        }).ToListAsync();

        }

        public async Task<UsuarioLiberacaoDescontoViewModel> ObterUsuarioLiberacaoDesconto(Guid operacaoId)
        {
            return await DbSet
                .Where(o => o.Id == operacaoId && o.UsuarioLiberacaoId.HasValue)
                .Select(o => new UsuarioLiberacaoDescontoViewModel
                {
                    Id = o.UsuarioLiberacao.Id,
                    Nome = o.UsuarioLiberacao.Nome,
                    DescontoMaximoPermitido = o.UsuarioLiberacao.DescontoMaximoPermitido,
                })
                .FirstOrDefaultAsync();
        }

        public async Task<bool> ClienteFornecedorTemConsignacaoEmAberto(Guid clienteFornecedorId, Guid lojaId)
        {
            return await DbSet.AnyAsync(o => o.ClienteFornecedorId.Equals(clienteFornecedorId) &&
                                         o.LojaId.Equals(lojaId) &&
                                         o.TipoOperacao.IdentificacaoTipoOperacao.Equals(IdentificacaoTipoOperacao.CONSIGNACAO) &&
                                         o.OperacaoItens.Any(oi => oi.Status == StatusOperacaoItem.EFETUADO) &&
                                         o.Status.Equals(StatusOperacao.EFETUADA));
        }

        public async Task VincularUsuarioLiberacaoDesconto(Guid operacaoId, Guid usuarioLiberacaoId)
        {
            var operacao = await DbSet.FindAsync(operacaoId);

            operacao.UsuarioLiberacaoId = usuarioLiberacaoId;

            DbSet.Update(operacao);

            await SaveChanges();
        }

        public async Task<Operacao> ObterComMovimetacaoFinanceiraPorDocumento(string numeroDocumento, Guid lojaId)
        {
            return await DbSet.Where(o => o.NumeroDocumento.Equals(numeroDocumento) &&
                                          o.LojaId == lojaId &&
                                          o.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_RECEBER)
                              .Include(o => o.MovimentacoesFinanceiras)
                              .FirstOrDefaultAsync();
        }
    }
}
