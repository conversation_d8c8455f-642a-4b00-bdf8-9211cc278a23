﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Dtos.Export.Request;
using Zendar.Business.API.ZendarSync.Dtos.Link.Request;
using Zendar.Business.API.ZendarSync.Dtos.Order.Request;
using Zendar.Business.API.ZendarSync.Dtos.Product.Request;
using Zendar.Business.API.ZendarSync.Dtos.Update.Request;
using Zendar.Business.Services.V1.IntegracaoServices.IntegracaoPausarService;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.IntegracaoSnapshot;
using Zendar.Business.ViewModels.Integracao.Tray;
using Zendar.Business.ViewModels.V1.Integracao.Tray;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.V1.IntegracaoServices.Tray.Interfaces
{
    public interface IIntegracaoTrayService : IDisposable, IAtivarInativarIntegracaoService
    {
        #region Etapa

        Task<IdentificacaoEtapasTray> ObterIdentificacaoEtapa();

        Task CadastrarEtapa(
            IntegracaoViewModel integracaoViewModel);

        Task AlterarEtapa(
            IntegracaoViewModel integracaoViewModel);

        Task AlterarIdentificacaoEtapa(
            IdentificacaoEtapasTray identificacaoEtapasTray);

        Task NotificarImportacaoEtapa(
            NotificacaoViewModel notificacaoViewModel);

        Task NotificarExportacaoEtapa(
            NotificacaoViewModel notificacaoViewModel);

        Task NotificarExportacaoLimite(
            NotificacaoViewModel notificacaoViewModel);

        Task NotificarPedido(
            NotificacaoViewModel notificacaoViewModel);

        Task NotificarAtualizacaoTabelaPreco(
            NotificacaoViewModel notificacaoViewModel);

        Task NotificarAtualizacaoPromocao(
            NotificacaoViewModel notificacaoViewModel);

        #endregion

        #region Vincular

        Task VincularMarca(
           VincularCadastroRequest vincularCadastroRequest);

        Task VincularCategoria(
            VincularCadastroRequest vincularCadastroRequest);

        Task VincularCaracteristica(
            VincularCadastroRequest vincularCadastroRequest);

        Task VincularVariacao(
            VincularCadastroRequest vincularCadastroRequest);

        Task VincularProduto(
            VincularCadastroRequest vincularCadastroRequest);

        Task VincularCliente(
			VincularCadastroRequest vincularCadastroRequest);

        Task VincularFormaPagamento(
            VincularCadastroRequest vincularCadastroRequest);

        #endregion

        #region Ação
        Task PublicarProduto(
            PublicarProdutoRequest publicarProdutoRequest);

        Task ExcluirProduto(
            ExcluirProdutoRequest excluirProdutoRequest);

        Task AlterarRastreioPedido(
            AlterarRastreioPedidoRequest alterarRastreioPedidoRequest);

        Task FinalizarPedido(
          FinalizarPedidoRequest finalizarPedidoRequest);

        Task AutenticarDados(
            DadosAutenticacaoViewModel dadosAutenticacaoViewModel);

        Task AlterarCanalVenda(
            DadosCanalVendaViewModel dadosCanalVendaViewModel);

        Task AlterarVendedor(Guid vendedorId);

        Task AlterarComissaoVenda(
            DadosComissaoVendaViewModel dadosComissaoVendaViewModel);

        Task AlterarLocalEstoque(
            Guid localEstoqueId);

        Task CopiarTabelaPreco(
            Guid tabelaPrecoId);

        Task AlterarTabelaPreco(
            Guid tabelaPrecoId);

        Task AlterarPromocao(
            Guid promocaoId);

        Task AlterarTipoCadastro(
            DadosTipoCadastroViewModel dadosTipoCadastroViewModel);

        Task AlterarBuscarProduto(
            DadosBuscarProdutoViewModel dadosBuscarProdutoViewModel);

        Task ExportarCadastroProduto(
            ExportarCadastroProdutoRequest exportarCadastroProdutoRequest);

        Task ImportarCadastroProduto(
            bool ignorarReferenciaEan);

        Task<Guid> ImportarProduto(
			string siteId,
			Guid lojaId,
            bool withTransaction = true);

        Task AtualizacaoTabelaPreco(
            AtualizacaoTabelaPrecoRequest atualizacaoTabelaPrecoRequest);

        Task AtualizacaoPromocao(
            AtualizacaoPromocaoRequest atualizacaoPromocaoRequest);

        Task<Guid?> CadastrarProduto(
            string cadastroPlataformaId);

        Task Desistir();

        #endregion

        #region Obter

        Task<Guid?> ObterId();

        Task<IntegracaoObterViewModel> Obter(Guid? lojaId = null);

        Task<IntegracaoTotalizadorViewModel> ObterTotalizador(int mes);

        Task<int> ObterQuantidadePendencia();

        Task<Guid?> ObterLocalEstoque();

        Task<DadosAutenticacaoViewModel> ObterAutenticacao();

        Task<DadosCanalVendaViewModel> ObterCanalVenda();

        Task<Guid?> ObterVendedor();

        Task<DadosComissaoVendaViewModel> ObterComissaoVenda();

        Task<Guid?> ObterTabelaPreco();

        Task<Guid?> ObterPromocao();

        Task<DadosTipoCadastroViewModel> ObterTipoCadastro();

        Task<DadosBuscarProdutoViewModel> ObterBuscarProduto();

        Task<int> ObterQuantidadeNotaFiscalAlerta();

        Task<QuantidadeProdutoViewModel> ObterQuantidadeProduto();

        Task<List<ListaIdNomeEnderecoViewModel>> ObterListaClienteCpfPendencia(
            Guid integracaoPedidoId);

        Task<GridPaginadaRetorno<ProdutoV2ViewModel>> ObterListaProdutoSitePaginado(
            GridPaginadaConsulta gridPaginada,
            ProdutoFiltrosViewModel produtoFiltrosViewModel,
            bool cadastro);

        Task<GridPaginadaRetorno<EntidadeSnapshotViewModel>> ObterListaMarcaSnapshot(
            GridPaginadaConsulta gridPaginada,
            string nome);

        Task<List<CategoriaSnapshotViewModel>> ObterListaCategoriaSnapshot(
            string nome);

        Task<List<EntidadeSnapshotViewModel>> ObterListaFormaPagamentoSnapshot(
            string nome);

        Task<GridPaginadaRetorno<ProdutoSnapshotViewModel>> ObterListaProdutoSnapshot(
            GridPaginadaConsulta gridPaginada,
            string nome);

        Task<Guid?> ObterVinculoProduto(string siteId);

        Task<Guid?> ObterVinculoVariacao(string siteId);
		#endregion
	}
}
