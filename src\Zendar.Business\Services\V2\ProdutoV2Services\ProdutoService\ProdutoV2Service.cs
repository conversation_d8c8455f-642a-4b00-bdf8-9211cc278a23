﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Data.Repositories.NcmRepositories;
using Multiempresa.Shared.Constants;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers.Convertores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Dtos.Export.Request;
using Zendar.Business.Application.Events.ProdutoEvents;
using Zendar.Business.AutoMapper.PromocaoMappers;
using Zendar.Business.AutoMappers.ProdutoMapper;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoV2Service;
using Zendar.Business.Validations.Models;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Business.ViewModels.V2.Promocao;
using Zendar.Data.DomainServices.PrecoServices;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.CampanhaPromocional;
using Zendar.Data.Models.DTO.Padronizacao;
using Zendar.Data.Models.DTO.Preco;
using Zendar.Data.Models.DTO.TabelaPreco;
using Zendar.Data.Models.GerenciadorDeImpressao;
using Zendar.Data.Repository.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.V2.ProdutoV2Services.ProdutoService
{
    public class ProdutoV2Service : BaseService, IProdutoV2Service
    {
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IStorageService _storageService;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ITabelaPrecoV2Service _tabelaPrecoV2Service;
        private readonly IPadronizacaoService _padronizacaoService;
        private readonly IIntegracaoService _integracaoService;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly INcmRepository _ncmRepository;
        private readonly IProdutoRegraFiscalExcecaoRepository _produtoRegraFiscalExcecaoRepository;
        private readonly IProdutoOrigemCombustivelRepository _produtoOrigemCombustivelRepository;
        private readonly IPromocaoItemRepository _promocaoItemRepository;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IPadronizacaoRepository _padronizacaoRepository;

        public ProdutoV2Service(
                  INotificador notificador,
                  ILogAuditoriaService logAuditoriaService,
                  IStorageService storageService,
                  ICategoriaProdutoService categoriaProdutoService,
                  ITabelaPrecoV2Service tabelaPrecoV2Service,
                  IPadronizacaoService padronizacaoService,
                  IIntegracaoService integracaoService,
                  IProdutoRepository produtoRepository,
                  IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
                  INcmRepository ncmRepository,
                  IProdutoRegraFiscalExcecaoRepository produtoRegraFiscalExcecaoRepository,
                  IProdutoOrigemCombustivelRepository produtoOrigemCombustivelRepository,
                  IPromocaoItemRepository promocaoItemRepository,
                  IAspNetUserInfo aspNetUserInfo,
                  IPadronizacaoRepository padronizacaoRepository)
                  : base(notificador)
        {
            _logAuditoriaService = logAuditoriaService;
            _storageService = storageService;
            _categoriaProdutoService = categoriaProdutoService;
            _tabelaPrecoV2Service = tabelaPrecoV2Service;
            _padronizacaoService = padronizacaoService;
            _integracaoService = integracaoService;
            _produtoRepository = produtoRepository;
            _ncmRepository = ncmRepository;
            _aspNetUserInfo = aspNetUserInfo;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
            _produtoRegraFiscalExcecaoRepository = produtoRegraFiscalExcecaoRepository;
            _produtoOrigemCombustivelRepository = produtoOrigemCombustivelRepository;
            _promocaoItemRepository = promocaoItemRepository;
            _padronizacaoRepository = padronizacaoRepository;
        }

        public void Dispose()
        {
            _logAuditoriaService?.Dispose();

            _produtoRepository?.Dispose();
            _produtoCorTamanhoRepository?.Dispose();
            _ncmRepository?.Dispose();
        }

        #region [Obter]
        public async Task<ProdutoV2ViewModel> Obter(Guid id)
        {
            var produto = await _produtoRepository.FindByKey(id);

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return null;
            }

            if (!string.IsNullOrEmpty(produto.Foto))
                produto.Foto = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produto.Foto);

            if (!string.IsNullOrEmpty(produto.ImagemCardapio))
                produto.ImagemCardapio = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produto.ImagemCardapio);

            if (!string.IsNullOrEmpty(produto.ImagemDestaque))
                produto.ImagemDestaque = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produto.ImagemDestaque);

            return produto.ToViewModel();
        }

        public async Task<ProdutoFiscaisV2ViewModel> ObterInformacoesFiscais(Guid id)
        {
            var produto = await _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    produto => produto.Id == id,
                    produto => new Produto
                    {
                        Id = produto.Id,

                        RegraFiscalId = produto.RegraFiscalId,
                        RegraFiscal = new RegraFiscal
                        {
                            Id = produto.RegraFiscal.Id,
                            Nome = produto.RegraFiscal.Nome
                        },
                        IcmsStRetidoBaseCalculo = produto.IcmsStRetidoBaseCalculo,
                        IcmsStRetidoValor = produto.IcmsStRetidoValor,
                        FcpStRetidoBaseCalculo = produto.FcpStRetidoBaseCalculo,
                        FcpStRetidoValor = produto.FcpStRetidoValor,

                        IcmsAliquota = produto.IcmsAliquota,
                        PisAliquota = produto.PisAliquota,
                        CofinsAliquota = produto.CofinsAliquota,
                        FcpAliquota = produto.FcpAliquota,
                        IcmsReducaoBaseCalculo = produto.IcmsReducaoBaseCalculo,
                        CodigoBeneficioFiscal = produto.CodigoBeneficioFiscal,

                        UnidadeTributavelId = produto.UnidadeTributavelId,
                        UnidadeTributavel = new UnidadeMedida
                        {
                            Id = produto.UnidadeTributavel.Id,
                            Descricao = produto.UnidadeTributavel.Descricao
                        },
                        QtdeConversao = produto.QtdeConversao,
                        FatorConversao = produto.FatorConversao,

                        CNPJFabricante = produto.CNPJFabricante,
                        IndicadorEscalaRelevante = produto.IndicadorEscalaRelevante,

                        CodigoAnp = produto.CodigoAnp,
                        CODIF = produto.CODIF,
                        PercentualGLP = produto.PercentualGLP,
                        PercentualGasNacional = produto.PercentualGasNacional,
                        PercentualGasImportado = produto.PercentualGasImportado,
                        ValorPartidaGLP = produto.ValorPartidaGLP,
                        AliquotaAdREmICMSRetido = produto.AliquotaAdREmICMSRetido,
                        QuantidadeBCMonoRetido = produto.QuantidadeBCMonoRetido,

                        CodigoNcm = produto.CodigoNcm,
                        CodigoCest = produto.CodigoCest,

                        CstOrigem = produto.CstOrigem,
                        TipoProdutoFiscal = produto.TipoProdutoFiscal,

                        ProdutoRegraFiscalExcecoes = produto.ProdutoRegraFiscalExcecoes,
                        ProdutosOrigemCombustivel = produto.ProdutosOrigemCombustivel
                    });

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return null;
            }

            var informacoesFiscais = produto.ToViewModel().InformacoesFiscais;
            var ncm = _ncmRepository.ObterPorCodigo(produto.CodigoNcm);
            if (ncm != null)
            {
                informacoesFiscais.NcmLabel = $"{ncm.Codigo} - {ncm.Descricao}";
            }

            return informacoesFiscais;
        }

        public async Task<ProdutoV2ViewModel> ObterInformacoesAdicionais(Guid id)
        {
            var produto = await _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    produto => produto.Id == id,
                    produto => new Produto
                    {
                        Id = produto.Id,
                        ControlaEstoque = produto.ControlaEstoque,
                        PermiteAlteraValorNaVenda = produto.PermiteAlteraValorNaVenda,
                        SolicitarInformacaoComplementarNoPdv = produto.SolicitarInformacaoComplementarNoPdv,
                        UtilizarBalanca = produto.UtilizarBalanca,
                        ExportarBalanca = produto.ExportarBalanca,
                        CodigoIntegracao = produto.CodigoIntegracao,
                    });

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return null;
            }

            return produto.ToViewModel();
        }

        public async Task<ProdutoInformacoesFoodV2ViewModel> ObterInformacoesFood(Guid id, Guid lojaId)
        {
            var produto = await _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    p => p.Id == id,
                    p => new Produto
                    {
                        Id = p.Id,
                        UtilizarBalanca = p.UtilizarBalanca,
                        ExportarBalanca = p.ExportarBalanca,
                        UsarComoComplemento = p.UsarComoComplemento,
                        ProdutoCombo = p.ProdutoCombo,
                        CobrarTaxaServico = p.CobrarTaxaServico,
                        BaixarSaldoMateriaPrima = p.BaixarSaldoMateriaPrima,
                        UtilizarPrecoDosItensEtapa = p.UtilizarPrecoDosItensEtapa,
                        ComposicaoProduto = p.ComposicaoProduto,
                        DiasParaValidade = p.DiasParaValidade,
                        PrecoCombo = p.PrecoCombo,
                        ImagemCardapio = p.ImagemCardapio,
                        ImagemDestaque = p.ImagemDestaque,
                        GerenciadoresDeImpressao = p.GerenciadoresDeImpressao
                        .Where(g => g.GerenciadorImpressao.LojaId == lojaId)
                        .Select(g => new ProdutoGerenciadorImpressao
                        {
                            GerenciadorImpressao = new GerenciadorImpressao
                            {
                                Id = g.GerenciadorImpressao.Id,
                                Nome = g.GerenciadorImpressao.Nome,
                            }
                        })
                        .ToList()
                    });

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return null;
            }

            if (!string.IsNullOrEmpty(produto.ImagemCardapio))
                produto.ImagemCardapio = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produto.ImagemCardapio);

            if (!string.IsNullOrEmpty(produto.ImagemDestaque))
                produto.ImagemDestaque = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produto.ImagemDestaque);

            return produto.ToViewModel().InformacoesFood;
        }

        public async Task<Produto> ObterComPredicate(
           Expression<Func<Produto, bool>> predicate = null)
        {
            try
            {
                return await _produtoRepository.FirstOrDefaultAsNoTracking(predicate);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<ProdutoV2ViewModel>> ObterListaProdutoGrid(
            GridPaginadaConsulta gridPaginada,
            ProdutoFiltrosViewModel produtoFiltrosViewModel,
            IdentificacaoIntegracao identificacaoIntegracao)
        {
            if (produtoFiltrosViewModel.CategoriasProduto != null)
            {
                var listaIdCategorias = new List<Guid>();

                foreach (var categoriaId in produtoFiltrosViewModel.CategoriasProduto)
                    listaIdCategorias.AddRange(_categoriaProdutoService.ObterCategoriasVinculadas(categoriaId).Result);

                produtoFiltrosViewModel.CategoriasProduto = listaIdCategorias;
            }

            var listaProdutoPaginado =
                 await _produtoRepository.ObterListaProdutoGrid(Guid.Parse(_aspNetUserInfo.LojaId.ToString()),
                                                                gridPaginada,
                                                                produtoFiltrosViewModel,
                                                                identificacaoIntegracao);

            var listaProdutoV2ViewModel
                = listaProdutoPaginado?.Select(c => c.ToViewModel())
                                      ?.ToList();

            return listaProdutoV2ViewModel;
        }

        public async Task<ProdutoV2ViewModel> ObterProdutoIntegracaoCompleto(
            Guid id,
            IdentificacaoIntegracao identificacaoIntegracao)
        {
            try
            {
                var integracao = await _integracaoService.Obter(identificacaoIntegracao, Guid.Parse(_aspNetUserInfo.LojaId.ToString()));

                if (integracao == null)
                    return null;

                if (integracao.TabelaPrecoId == null ||
                    integracao.TabelaPrecoId == Guid.Empty)
                    return null;

                var produto =
                    await _produtoRepository.ObterProdutoIntegracaoCompleto(Guid.Parse(_aspNetUserInfo.LojaId.ToString()),
                                                                            id,
                                                                            identificacaoIntegracao,
																			integracao.LocalEstoqueId);

                var produtoV2ViewModel = produto?.ToViewModel();

                TabelaPrecoDTO tabelaPrecoIntegracaoDTO = await _tabelaPrecoV2Service.Obter(integracao.TabelaPrecoId.Value);

                var padronizacao = await ObterPadronizacaoCasasDecimais();

                if (produtoV2ViewModel != null)
                {
                    var precoDTO = produtoV2ViewModel?.Precos
                                                     ?.ProdutoPrecoLojas
                                                     ?.Where(y => y.LojaId == _aspNetUserInfo.LojaId.Value)
                                                     ?.Select(y => new PrecoDTO(y.PrecoVenda.PrecoVenda, y.PrecoCusto))
                                                     ?.FirstOrDefault();

                    produtoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        foreach (var produtoCorImagemV2ViewModel in produtoCorV2ViewModel.ProdutoCorImagens)
                        {
                            var imagemUrl = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produtoCorImagemV2ViewModel.Imagem);

                            if (!string.IsNullOrEmpty(imagemUrl))
                                produtoCorImagemV2ViewModel.Imagem = imagemUrl;
                        }

                        foreach (var produtoCorTamanhoV2ViewModel in produtoCorV2ViewModel.ProdutoCorTamanhos)
                        {
                            if (precoDTO != null)
                            {
                                var precoVariacao = new ProdutoVariacaoPrecoDTO(produtoCorTamanhoV2ViewModel.Id, produtoV2ViewModel.Id, precoDTO);

                                if (precoVariacao != null)
                                    produtoCorTamanhoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, precoVariacao, padronizacao);
                            }
                        }
                    }
                }

                return produtoV2ViewModel;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<ProdutoV2ViewModel>> ObterListaProdutoIntegracaoCompleto(
            bool? ativo,
            IdentificacaoIntegracao identificacaoIntegracao)
        {
            try
            {
                var integracao = await _integracaoService.Obter(identificacaoIntegracao, Guid.Parse(_aspNetUserInfo.LojaId.ToString()));

                if (integracao == null)
                    return null;

                if (integracao.TabelaPrecoId == null ||
                    integracao.TabelaPrecoId == Guid.Empty)
                    return null;

                var listaProduto =
                    await _produtoRepository.ObterListaProdutoIntegracaoCompleto(Guid.Parse(_aspNetUserInfo.LojaId.ToString()),
                                                                                 ativo,
                                                                                 identificacaoIntegracao,
                                                                                 integracao.LocalEstoqueId);

                var listaProdutoV2ViewModel = listaProduto?.Select(c => c.ToViewModel())
                                                          ?.ToList();

                TabelaPrecoDTO tabelaPrecoIntegracaoDTO = await _tabelaPrecoV2Service.Obter(integracao.TabelaPrecoId.Value);

                var padronizacao = await ObterPadronizacaoCasasDecimais();

                foreach (var produtoV2ViewModel in listaProdutoV2ViewModel)
                {
                    var precoDTO = produtoV2ViewModel?.Precos
                                                     ?.ProdutoPrecoLojas
                                                     ?.Where(y => y.LojaId == _aspNetUserInfo.LojaId.Value)
                                                     ?.Select(y => new PrecoDTO(y.PrecoVenda.PrecoVenda, y.PrecoCusto))
                                                     ?.FirstOrDefault();

                    produtoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        foreach (var produtoCorImagemV2ViewModel in produtoCorV2ViewModel.ProdutoCorImagens)
                        {
                            var imagemUrl = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produtoCorImagemV2ViewModel.Imagem);

                            if (!string.IsNullOrEmpty(imagemUrl))
                                produtoCorImagemV2ViewModel.Imagem = imagemUrl;
                        }

                        foreach (var produtoCorTamanhoV2ViewModel in produtoCorV2ViewModel.ProdutoCorTamanhos)
                        {
                            if (precoDTO != null)
                            {
                                var precoVariacao = new ProdutoVariacaoPrecoDTO(produtoCorTamanhoV2ViewModel.Id, produtoV2ViewModel.Id, precoDTO);

                                if (precoVariacao != null)
                                    produtoCorTamanhoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, precoVariacao, padronizacao);
                            }
                        }
                    }
                }

                return listaProdutoV2ViewModel;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<ProdutoV2ViewModel>> ObterListaProdutoIntegracaoFiltroCompleto(
            bool? ativo,
            IdentificacaoIntegracao identificacaoIntegracao,
            List<ExportarProdutoRequest> listaExportarProdutoRequest)
        {
            try
            {
                var integracao = await _integracaoService.Obter(identificacaoIntegracao, Guid.Parse(_aspNetUserInfo.LojaId.ToString()));

                if (integracao == null)
                    return null;

                if (integracao.TabelaPrecoId == null ||
                    integracao.TabelaPrecoId == Guid.Empty)
                    return null;

                List<Produto> listaProduto = new List<Produto>();

                foreach (var exportarProdutoRequest in listaExportarProdutoRequest)
                {
                    var produtoTemp =
                        await _produtoRepository.ObterProdutoIntegracaoCompleto(Guid.Parse(_aspNetUserInfo.LojaId.ToString()),
                                                                                exportarProdutoRequest.ProdutoId,
                                                                                identificacaoIntegracao,
																				integracao.LocalEstoqueId);

                    if (produtoTemp != null)
                    {
                        List<Produto> listaProdutoTemp = new List<Produto>
                        {
                            produtoTemp
                        };

                        listaProdutoTemp = exportarProdutoRequest.ListaProdutoCorTamanhoId.Any() ?
                                                        listaProdutoTemp.Where(c => c.ProdutoCores
                                                                        .Any(y => y.ProdutoCorTamanhos
                                                                        .Any(z => exportarProdutoRequest.ListaProdutoCorTamanhoId.Contains(z.Id))))
                                                                        .ToList() :
                                                        listaProdutoTemp;


                        listaProduto.AddRange(listaProdutoTemp);
                    }
                }

                var listaProdutoV2ViewModel = listaProduto?.Select(c => c.ToViewModel())
                                                          ?.ToList();

                TabelaPrecoDTO tabelaPrecoIntegracaoDTO = await _tabelaPrecoV2Service.Obter(integracao.TabelaPrecoId.Value);

                var padronizacao = await ObterPadronizacaoCasasDecimais();

                foreach (var produtoV2ViewModel in listaProdutoV2ViewModel)
                {
                    var precoDTO = produtoV2ViewModel?.Precos
                                                     ?.ProdutoPrecoLojas
                                                     ?.Where(y => y.LojaId == _aspNetUserInfo.LojaId.Value)
                                                     ?.Select(y => new PrecoDTO(y.PrecoVenda.PrecoVenda, y.PrecoCusto))
                                                     ?.FirstOrDefault();

                    produtoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        foreach (var produtoCorImagemV2ViewModel in produtoCorV2ViewModel.ProdutoCorImagens)
                        {
                            var imagemUrl = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produtoCorImagemV2ViewModel.Imagem);

                            if (!string.IsNullOrEmpty(imagemUrl))
                                produtoCorImagemV2ViewModel.Imagem = imagemUrl;
                        }

                        foreach (var produtoCorTamanhoV2ViewModel in produtoCorV2ViewModel.ProdutoCorTamanhos)
                        {
                            if (precoDTO != null)
                            {
                                var precoVariacao = new ProdutoVariacaoPrecoDTO(produtoCorTamanhoV2ViewModel.Id, produtoV2ViewModel.Id, precoDTO);

                                if (precoVariacao != null)
                                    produtoCorTamanhoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, precoVariacao, padronizacao);
                            }
                        }
                    }
                }

                return listaProdutoV2ViewModel;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<ProdutoV2ViewModel>> ObterListaProdutoIntegracaoPaginadoCompleto(
            GridPaginadaConsulta gridPaginada,
            IdentificacaoIntegracao identificacaoIntegracao)
        {
            try
            {
                var integracao = await _integracaoService.Obter(identificacaoIntegracao, Guid.Parse(_aspNetUserInfo.LojaId.ToString()));

                if (integracao == null)
                    return null;

                if (integracao.TabelaPrecoId == null ||
                    integracao.TabelaPrecoId == Guid.Empty)
                    return null;

                var listaProduto =
                    _produtoRepository.ObterListaProdutoIntegracaoPaginadoCompleto(Guid.Parse(_aspNetUserInfo.LojaId.ToString()),
                                                                                   gridPaginada,
                                                                                   identificacaoIntegracao,
																				   integracao.LocalEstoqueId);

                var listaProdutoV2ViewModel = listaProduto?.Registros?.Select(c => c.ToViewModel())
                                                                     ?.ToList();

                TabelaPrecoDTO tabelaPrecoIntegracaoDTO = await _tabelaPrecoV2Service.Obter(integracao.TabelaPrecoId.Value);

                var padronizacao = await ObterPadronizacaoCasasDecimais();

                foreach (var produtoV2ViewModel in listaProdutoV2ViewModel)
                {
                    var precoDTO = produtoV2ViewModel?.Precos
                                                     ?.ProdutoPrecoLojas
                                                     ?.Where(y => y.LojaId == _aspNetUserInfo.LojaId.Value)
                                                     ?.Select(y => new PrecoDTO(y.PrecoVenda.PrecoVenda, y.PrecoCusto))
                                                     ?.FirstOrDefault();

                    produtoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        foreach (var produtoCorImagemV2ViewModel in produtoCorV2ViewModel.ProdutoCorImagens)
                        {
                            var imagemUrl = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produtoCorImagemV2ViewModel.Imagem);

                            if (!string.IsNullOrEmpty(imagemUrl))
                                produtoCorImagemV2ViewModel.Imagem = imagemUrl;
                        }

                        foreach (var produtoCorTamanhoV2ViewModel in produtoCorV2ViewModel.ProdutoCorTamanhos)
                        {
                            if (precoDTO != null)
                            {
                                var precoVariacao = new ProdutoVariacaoPrecoDTO(produtoCorTamanhoV2ViewModel.Id, produtoV2ViewModel.Id, precoDTO);

                                if (precoVariacao != null)
                                    produtoCorTamanhoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, precoVariacao, padronizacao);
                            }
                        }
                    }
                }

                return listaProdutoV2ViewModel;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<ProdutoV2ViewModel>> ObterListaProdutoTabelaPrecoIntegracaoPaginadoCompleto(
            GridPaginadaConsulta gridPaginada,
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid tabelaPrecoId)
        {
            try
            {
                var integracao = await _integracaoService.Obter(identificacaoIntegracao, Guid.Parse(_aspNetUserInfo.LojaId.ToString()));

                if (integracao == null)
                    return null;

                var listaProduto =
                    _produtoRepository.ObterListaProdutoIntegracaoPaginadoCompleto(Guid.Parse(_aspNetUserInfo.LojaId.ToString()),
                                                                                   gridPaginada,
                                                                                   identificacaoIntegracao,
																				   integracao.LocalEstoqueId);

                var listaProdutoV2ViewModel = listaProduto?.Registros?.Select(c => c.ToViewModel())
                                                                     ?.ToList();

                TabelaPrecoDTO tabelaPrecoIntegracaoDTO = await _tabelaPrecoV2Service.Obter(tabelaPrecoId);

                var padronizacao = await ObterPadronizacaoCasasDecimais();

                foreach (var produtoV2ViewModel in listaProdutoV2ViewModel)
                {
                    var precoDTO = produtoV2ViewModel?.Precos
                                                     ?.ProdutoPrecoLojas
                                                     ?.Where(y => y.LojaId == _aspNetUserInfo.LojaId.Value)
                                                     ?.Select(y => new PrecoDTO(y.PrecoVenda.PrecoVenda, y.PrecoCusto))
                                                     ?.FirstOrDefault();

                    produtoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, new ProdutoVariacaoPrecoDTO(Guid.Empty, produtoV2ViewModel.Id, precoDTO), padronizacao);

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        foreach (var produtoCorTamanhoV2ViewModel in produtoCorV2ViewModel.ProdutoCorTamanhos)
                        {
                            if (precoDTO != null)
                            {
                                var precoVariacao = new ProdutoVariacaoPrecoDTO(produtoCorTamanhoV2ViewModel.Id, produtoV2ViewModel.Id, precoDTO);

                                if (precoVariacao != null)
                                    produtoCorTamanhoV2ViewModel.PrecoAVista = PrecoProdutoCalculador.Calcular(tabelaPrecoIntegracaoDTO, precoVariacao, padronizacao);
                            }
                        }
                    }
                }

                return listaProdutoV2ViewModel;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<PromocaoItemIntegracaoViewModel>> ObterListaProdutoPromocaoIntegracaoPaginadoCompleto(
            GridPaginadaConsulta gridPaginada,
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid promocaoId)
        {
            try
            {
                var integracao = await _integracaoService.Obter(identificacaoIntegracao, Guid.Parse(_aspNetUserInfo.LojaId.ToString()));

                if (integracao == null)
                    return null;

                var promocaoItens = await _promocaoItemRepository.ObterPromocaoItemIntegracaoPaginado(promocaoId, gridPaginada);

                var listaTelaUsoPromocao = promocaoItens.First().Promocao.ObterTelasUsoSistema();

                var telaUsoPromocaoTray =
                    listaTelaUsoPromocao?.Any(c => c == TelaUsoPromocao.TRAY);

                if (!telaUsoPromocaoTray.Value)
                    return null;

                var padronizacao = await ObterPadronizacaoCasasDecimais();

                return promocaoItens
                    .Select(pi => new PromocaoItemIntegracaoViewModel
                    {
                        InicioPromocao = pi.Promocao.VigenciaInicio,
                        FimPromocao = pi.Promocao.VigenciaFim,
                        PrecoPromocao = Math.Round(pi.PrecoVenda, padronizacao.CasasDecimaisValor),
                        ProdutoId = pi.ProdutoId,
                        ProdutoCorTamanhoId = pi.ProdutoCorTamanhoId,
                        TipoProduto = pi.ProdutoCorTamanho.ProdutoCor.Produto.TipoProduto
                    })
                    .ToList();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<ProdutoVideoV2ViewModel> ObterVideo(Guid id)
        {
            var produto = await _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    p => p.Id == id,
                    p => new Produto
                    {
                        VideoUrl = p.VideoUrl,
                        VideoThumbnailUrl = p.VideoThumbnailUrl
                    });

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("preoduto");
                return default;
            }

            return new ProdutoVideoV2ViewModel()
            {
                VideoUrl = produto.VideoUrl,
                VideoThumbnailUrl = produto.VideoThumbnailUrl
            };
        }

        #endregion

        #region [CRUD]
        public async Task<Guid> Cadastrar(ProdutoV2ViewModel produtoViewModel, bool executarValidacao)
        {
            Produto produto = produtoViewModel.ToModel();
            produto.Id = Guid.Empty;

            if (!await ValidarProdutoParaCadastroAlteracao(produto, executarValidacao))
                return default;

            if (!string.IsNullOrEmpty(produto.Foto))
            {
                var caminhoImagem = Produto.GerarCaminhoImagem(CaminhoArquivosStorage.CaminhoImagemProduto);
                if (!await EnviarFoto(caminhoImagem, produto.Foto))
                    return default;

                produto.Foto = caminhoImagem;
            }

            // Imagem Destaque
            if (!string.IsNullOrEmpty(produto.ImagemDestaque))
            {
                var caminhoImagem = Produto.GerarCaminhoImagem(CaminhoArquivosStorage.CaminhoImagemProdutoDestaque);
                if (!await EnviarFoto(caminhoImagem, produto.ImagemDestaque))
                    return default;

                produto.ImagemDestaque = caminhoImagem;
            }

            // Imagem Cardapio
            if (!string.IsNullOrEmpty(produto.ImagemCardapio))
            {
                var caminhoImagem = Produto.GerarCaminhoImagem(CaminhoArquivosStorage.CaminhoImagemProdutoCardapio);
                if (!await EnviarFoto(caminhoImagem, produto.ImagemCardapio))
                    return default;

                produto.ImagemCardapio = caminhoImagem;
            }

            await _produtoRepository.Insert(produto);

            if (produto.CodigoIntegracao == 0)
            {
                produto.CodigoIntegracao = await GerarCodigoIntegracao(produto);
				await _produtoRepository.SaveChanges();
			}

            await _logAuditoriaService
               .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.CADASTRAR, $"Nome: {produto.Nome}"));

            return produto.Id;
        }

        public async Task Alterar(ProdutoV2ViewModel produtoVm, bool executarValidacao)
        {
            if (!await ValidarProdutoParaCadastroAlteracao(produtoVm.ToModel(), executarValidacao))
                return;

            var produto = await _produtoRepository
                .Where(p => p.Id == produtoVm.Id)
                .Include(p => p.ProdutoRegraFiscalExcecoes)
                .Include(p => p.ProdutosOrigemCombustivel)
                .Include(p => p.GerenciadoresDeImpressao).ThenInclude(p => p.GerenciadorImpressao)
                .FirstOrDefaultAsync();

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return;
            }

            if (produto.TipoProduto != produtoVm.TipoProduto &&
                !await ValidarAlteracaoDeTipoProduto(produto))
            {
                return;
            }

            // Imagem Destaque
            if (produto.ImagemDestaque != _storageService.ObterCaminhoArquivoBancoDados(StorageContaArmazenamento.Imagens, produtoVm.InformacoesFood.ImagemDestaque))
            {
                if (!string.IsNullOrEmpty(produto.ImagemDestaque))
                    await _storageService.Excluir(StorageContaArmazenamento.Imagens, produto.ImagemDestaque);

                if (!string.IsNullOrEmpty(produtoVm.InformacoesFood.ImagemDestaque))
                {
                    var caminhoImagem = Produto.GerarCaminhoImagem(CaminhoArquivosStorage.CaminhoImagemProdutoDestaque);

                    if (!await EnviarFoto(caminhoImagem, produtoVm.InformacoesFood.ImagemDestaque))
                        return;

                    produtoVm.InformacoesFood.ImagemDestaque = caminhoImagem;
                }
            }

            // Imagem Cardapio
            if (produto.ImagemCardapio != _storageService.ObterCaminhoArquivoBancoDados(StorageContaArmazenamento.Imagens, produtoVm.InformacoesFood.ImagemCardapio))
            {
                if (!string.IsNullOrEmpty(produto.ImagemCardapio))
                    await _storageService.Excluir(StorageContaArmazenamento.Imagens, produto.ImagemCardapio);

                if (!string.IsNullOrEmpty(produtoVm.InformacoesFood.ImagemCardapio))
                {
                    var caminhoImagem = Produto.GerarCaminhoImagem(CaminhoArquivosStorage.CaminhoImagemProdutoCardapio);

                    if (!await EnviarFoto(caminhoImagem, produtoVm.InformacoesFood.ImagemCardapio))
                        return;

                    produtoVm.InformacoesFood.ImagemCardapio = caminhoImagem;
                }
            }

            bool inativado = produto.Ativo && !produtoVm.Ativo;
            produto.Ativo = produtoVm.Ativo;
            produto.DataHoraUltimaAlteracao = DateTime.UtcNow;

            #region [Dados Gerais]
            produto.CategoriaProdutoId = produtoVm.CategoriaProdutoId;
            produto.MarcaId = produtoVm.MarcaId;
            produto.UnidadeMedidaId = produtoVm.UnidadeMedidaId;
            produto.TipoProduto = produtoVm.TipoProduto;
            produto.Nome = produtoVm.Nome;
            produto.NomeAbreviado = produtoVm.NomeAbreviado;
            produto.Referencia = produtoVm.Referencia;
            produto.Foto = produtoVm.Foto;
            produto.VideoUrl = produtoVm.VideoUrl;
            produto.VideoThumbnailUrl = produtoVm.VideoThumbnailUrl;
            produto.VenderEcommerce = produtoVm.VenderEcommerce;
            produto.CodigoIntegracao = produtoVm.CodigoIntegracao > 0 ? produtoVm.CodigoIntegracao.Value : produto.CodigoIntegracao;
            #endregion

            #region [Informações Fiscais]

            if (produto.ProdutoRegraFiscalExcecoes?.Any() ?? false)
            {
                await _produtoRegraFiscalExcecaoRepository.DeleteRange(produto.ProdutoRegraFiscalExcecoes.ToList());
                await _produtoRepository.SaveChanges();
            }

            if (produto.ProdutosOrigemCombustivel?.Any() ?? false)
            {
                await _produtoOrigemCombustivelRepository.DeleteRange(produto.ProdutosOrigemCombustivel.ToList());
                await _produtoRepository.SaveChanges();
            }

            produto.RegraFiscalId = produtoVm.InformacoesFiscais.RegraFiscalId;
            produto.IcmsStRetidoBaseCalculo = produtoVm.InformacoesFiscais.IcmsStRetidoBaseCalculo;
            produto.IcmsStRetidoValor = produtoVm.InformacoesFiscais.IcmsStRetidoValor;
            produto.FcpStRetidoBaseCalculo = produtoVm.InformacoesFiscais.FcpStRetidoBaseCalculo;
            produto.FcpStRetidoValor = produtoVm.InformacoesFiscais.FcpStRetidoValor;

            produto.IcmsAliquota = produtoVm.InformacoesFiscais.IcmsAliquota;
            produto.PisAliquota = produtoVm.InformacoesFiscais.PisAliquota;
            produto.CofinsAliquota = produtoVm.InformacoesFiscais.CofinsAliquota;
            produto.FcpAliquota = produtoVm.InformacoesFiscais.FcpAliquota;
            produto.IcmsReducaoBaseCalculo = produtoVm.InformacoesFiscais.IcmsReducaoBaseCalculo;
            produto.CodigoBeneficioFiscal = produtoVm.InformacoesFiscais.CodigoBeneficioFiscal;

            produto.UnidadeTributavelId = produtoVm.InformacoesFiscais.UnidadeTributavelId;
            produto.QtdeConversao = produtoVm.InformacoesFiscais.QtdeConversao;
            produto.FatorConversao = produtoVm.InformacoesFiscais.FatorConversao;

            produto.CNPJFabricante = produtoVm.InformacoesFiscais.CNPJFabricante;
            produto.IndicadorEscalaRelevante = produtoVm.InformacoesFiscais.IndicadorEscalaRelevante;

            produto.CodigoAnp = produtoVm.InformacoesFiscais.CodigoAnp;
            produto.CODIF = produtoVm.InformacoesFiscais.CODIF;
            produto.PercentualGLP = produtoVm.InformacoesFiscais.PercentualGLP;
            produto.PercentualGasNacional = produtoVm.InformacoesFiscais.PercentualGasNacional;
            produto.PercentualGasImportado = produtoVm.InformacoesFiscais.PercentualGasImportado;
            produto.ValorPartidaGLP = produtoVm.InformacoesFiscais.ValorPartidaGLP;
            produto.AliquotaAdREmICMSRetido = produtoVm.InformacoesFiscais.AliquotaAdREmICMSRetido;
            produto.QuantidadeBCMonoRetido = produtoVm.InformacoesFiscais.QuantidadeBCMonoRetido;

            produto.CodigoNcm = produtoVm.InformacoesFiscais.CodigoNcm;
            produto.CodigoCest = produtoVm.InformacoesFiscais.CodigoCest;

            produto.CstOrigem = produtoVm.InformacoesFiscais.CstOrigem;
            produto.TipoProdutoFiscal = produtoVm.InformacoesFiscais.TipoProdutoFiscal;
            produto.ProdutoRegraFiscalExcecoes = produtoVm.InformacoesFiscais.ProdutoRegraFiscalExcecoes
                                                                             .Select(produtoFiscal => new ProdutoRegraFiscalExcecao
                                                                             {
                                                                                 EstadoOrigemId = produtoFiscal.EstadoOrigemId,
                                                                                 EstadoDestinoId = produtoFiscal.EstadoDestinoId,
                                                                                 AliquotaIcms = produtoFiscal.AliquotaIcms,
                                                                                 CodigoBeneficioFiscal = produtoFiscal.CodigoBeneficioFiscal,
                                                                                 PorcentagemFCP = produtoFiscal.PorcentagemFCP,
                                                                                 ReducaoBaseCalculo = produtoFiscal.ReducaoBaseCalculo,
                                                                             }).ToList();

            produto.ProdutosOrigemCombustivel = produtoVm.InformacoesFiscais.ProdutoOrigemCombustivel
                                                                             .Select(o => new ProdutoOrigemCombustivel
                                                                             {
                                                                                 ProdutoId = produto.Id,
                                                                                 IndicadorImportacao = o.IndicadorImportacao,
                                                                                 CodigoUF = o.CodigoUF,
                                                                                 PercentualOriginario = o.PercentualOriginario,
                                                                             }).ToList();
            #endregion

            #region [Informações Adicionais]
            produto.ControlaEstoque = produtoVm.InformacoesAdicionais.ControlaEstoque;
            produto.PermiteAlteraValorNaVenda = produtoVm.InformacoesAdicionais.PermiteAlteraValorNaVenda;
            produto.UtilizarBalanca = produtoVm.InformacoesAdicionais.UtilizarBalanca;
            produto.ExportarBalanca = produtoVm.InformacoesAdicionais.ExportarBalanca;
            produto.SolicitarInformacaoComplementarNoPdv = produtoVm.InformacoesAdicionais.SolicitarInformacaoComplementarNoPdv;
            #endregion

            #region [Informações Food]
            produto.UsarComoComplemento = produtoVm.InformacoesFood.UsarComoComplemento;
            produto.ProdutoCombo = produtoVm.InformacoesFood.ProdutoCombo;
            produto.CobrarTaxaServico = produtoVm.InformacoesFood.CobrarTaxaServico;
            produto.BaixarSaldoMateriaPrima = produtoVm.InformacoesFood.BaixarSaldoMateriaPrima;
            produto.UtilizarPrecoDosItensEtapa = produtoVm.InformacoesFood.UtilizarPrecoDosItensEtapa;
            produto.ComposicaoProduto = produtoVm.InformacoesFood.ComposicaoProduto;
            produto.DiasParaValidade = produtoVm.InformacoesFood.DiasParaValidade;
            produto.PrecoCombo = produtoVm.InformacoesFood.PrecoCombo;
            produto.ImagemCardapio = produtoVm.InformacoesFood.ImagemCardapio;
            produto.ImagemDestaque = produtoVm.InformacoesFood.ImagemDestaque;

            if (produtoVm.InformacoesFood.GerenciadorImpressao is not null)
            {
                if (produtoVm.InformacoesFood.GerenciadorImpressao.GerenciadorImpressaoId.HasValue)
                {
                    produto.AdicionarGerenciador(
                        produtoVm.InformacoesFood.GerenciadorImpressao.GerenciadorImpressaoId.Value,
                        produtoVm.InformacoesFood.GerenciadorImpressao.LojaId);
                }
                else
                {
                    produto.RemoverGerenciador(produtoVm.InformacoesFood.GerenciadorImpressao.LojaId);
                }
            }
            #endregion

            _produtoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, inativado));

            await _produtoRepository.SaveChanges();

            await _logAuditoriaService
               .Inserir(new LogAuditoriaInserirViewModel(
                   LogAuditoriaTela.PRODUTO,
                   LogAuditoriaOperacao.ALTERAR,
                   $"Nome: {produto.Nome}"));
        }

        public async Task AlterarVideo(Guid produtoId, ProdutoVideoV2ViewModel videoV2ViewModel)
        {
            if (!Uri.TryCreate(videoV2ViewModel.VideoUrl, UriKind.Absolute, out Uri uri))
            {
                NotificarAviso("A url do vídeo informada não é uma url válida.");
                return;
            }

            var produto = await _produtoRepository.FindByKey(produtoId);

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return;
            }

            var logDescricao = $"Nome: {produto.Nome} | Vídeo anterior: {produto.VideoUrl} | Vídeo novo: {videoV2ViewModel.VideoUrl}";

            produto.VideoUrl = videoV2ViewModel.VideoUrl;
            produto.VideoThumbnailUrl = videoV2ViewModel.VideoThumbnailUrl;

            _produtoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, false));

            await _produtoRepository.SaveChanges();

            await _logAuditoriaService
               .Inserir(new LogAuditoriaInserirViewModel(
                   LogAuditoriaTela.PRODUTO,
                   LogAuditoriaOperacao.ALTERAR,
                   logDescricao));
        }

        public async Task Inativar(Guid produtoId)
        {
            var produto = await _produtoRepository.FindByKey(produtoId);

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return;
            }

            if (!produto.Ativo) return;

            produto.Ativo = false;

            _produtoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, true));

            await _produtoRepository.SaveChanges();

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(
                LogAuditoriaTela.PRODUTO,
                LogAuditoriaOperacao.ALTERAR,
                $"Nome: {produto.Nome}, Ativo > Inativo"));
        }

        public async Task Ativar(Guid produtoId)
        {
			var produto = await _produtoRepository.FindByKey(produtoId);

			if (produto == null)
			{
				NotificarAvisoRegistroNaoEncontrado("produto");
				return;
			}

			if (produto.Ativo) return;

			produto.Ativo = true;

			_produtoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, false));

			await _produtoRepository.SaveChanges();

			await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(
				LogAuditoriaTela.PRODUTO,
				LogAuditoriaOperacao.ALTERAR,
				$"Nome: {produto.Nome}, Inativo > Ativo"));
		}

		public async Task Excluir(Guid produtoId)
        {
            var produto = await _produtoRepository
                .FirstOrDefaultAsNoTracking(
                    x => x.Id.Equals(produtoId),
                    x => new Produto
                    {
                        Nome = x.Nome
                    });

            if (produto == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return;
            }

            _produtoRepository.AdicionarEvento(new ProdutoExcluidoEvent(produto.Id));

            await _produtoRepository.Delete(produtoId);

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(
                      LogAuditoriaTela.PRODUTO,
                      LogAuditoriaOperacao.REMOVER,
                      $"Nome: {produto.Nome}"));
        }
        #endregion

        /// <summary>
        /// Envia a imagem ao blob storage.
        /// Caso a imagem for uma Url, será baixada e enviada ao blob.
        /// </summary>
        /// <param name="caminhoImagem">'Path' da imagem para o blob</param>
        /// <param name="foto">Url ou base64 da iamgem</param>
        /// <returns>Retorna um booleano que informa se o processamento ocorreu sem falhas.</returns>
        private async Task<bool> EnviarFoto(string caminhoImagem, string foto)
        {
            //Verifica se a foto é uma URL
            if (Uri.TryCreate(foto, UriKind.Absolute, out Uri uri))
            {
                //Pega o base64 da URL
                var image = await ConverterArquivo.ConverterUrlImageBase64(foto);

                if (!image.Item1)
                {
                    NotificarAviso(ResourceMensagem.ProdutoService_FormatoImagemNaoPermitido);
                    return false;
                }

                foto = image.Item2;
            }

            await _storageService.Upload(StorageContaArmazenamento.Imagens, TipoArquivo.OUTROS, caminhoImagem, foto);
            return true;
        }

        private async Task<int> GerarCodigoIntegracao(Produto produto)
        {
			bool gerarCodigoIntegracao = (_aspNetUserInfo.PossuiServico(ReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA) ||
										 _aspNetUserInfo.PossuiServico(ReferenciaServicoStargate.DISPOSITIVO_PDV));

            if (!gerarCodigoIntegracao) return produto.SkuIdentificador;

			var numeroInicial = await _padronizacaoRepository.ObterAsync(p => p.NumeracaoInicialCodigoIntegracao);
			var ultimoNumeroUsado = await _produtoRepository.MaxAsync(p => p.CodigoIntegracao);

			return numeroInicial > ultimoNumeroUsado ? numeroInicial : ultimoNumeroUsado + 1;
		}

        #region [Validation]
        private async Task<PadronizacaoCasaDecimalDTO> ObterPadronizacaoCasasDecimais()
        {
            var padronizacao = await _padronizacaoService.ObterCasasDecimais();

            return padronizacao ?? PadronizacaoCasaDecimalDTO.CriarPadrao();
        }

        private async Task<bool> ValidarProdutoParaCadastroAlteracao(Produto produto, bool executarValidacaoNome)
        {
            if (!ValidarPalavrasReservadas(produto))
                return false;

            if (!ExecutarValidacao(new ProdutoValidation(), produto))
                return false;

            if (await _produtoRepository.Any(x => !string.IsNullOrEmpty(produto.Referencia)
                                                  && x.Referencia == produto.Referencia
                                                  && (produto.Id == Guid.Empty || produto.Id != x.Id)))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoReferenciaInformada);
                return false;
            }

            return true;
        }

        private bool ValidarPalavrasReservadas(Produto produto)
        {
            var palavrasReservadas = new string[] { "0X", "0x" };

            if (!string.IsNullOrEmpty(produto.Referencia) && palavrasReservadas.Any(x => produto.Referencia.StartsWith(x)))
            {
                NotificarAviso("O campo referência possui uma ou mais palavras reservadas em sua composição.");
                return false;
            }

            return true;
        }

        private async Task<bool> ValidarAlteracaoDeTipoProduto(
            Produto produto)
        {
            if (await _produtoCorTamanhoRepository.Any(pct => pct.ProdutoCor.ProdutoId == produto.Id && pct.OperacaoItens.Any()))
            {
                NotificarAviso("Não é possível alterar o tipo do produto, pois ele já foi movimentado.");
                return false;
            }

            if (produto.TipoProduto != TipoProduto.PRODUTO_SIMPLES)
            {
                if (produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                    await _produtoCorTamanhoRepository.Any(pct => pct.ProdutoCor.ProdutoId == produto.Id &&
                                                                  (!pct.Tamanho.PadraoSistema || !pct.ProdutoCor.Cor.PadraoSistema)))
                {
                    NotificarAviso("Não é possível alterar o tipo do produto, pois ele possui variações.");
                    return false;
                }

                if (produto.TipoProduto == TipoProduto.PRODUTO_KIT &&
                    await _produtoCorTamanhoRepository.Any(pct => pct.ProdutoCor.ProdutoId == produto.Id &&
                                                                  pct.ProdutoCorTamanhoPrincipal.Any()))
                {
                    NotificarAviso("Não é possível alterar o tipo do produto, pois ele possui itens em sua composição.");
                    return false;
                }
            }

            return true;
        }
        #endregion
    }
}
