﻿using Multiempresa.Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Models.Aplicacao;

namespace Zendar.Data.Interfaces.Aplicacao
{
	public interface IImportacaoCadastroIntegracaoRepository : IRepository<ImportacaoCadastroIntegracao>
	{
		Task<List<ImportacaoCadastroIntegracao>> ObterRegistrosPorLoja(
			Guid lojaId,
			TipoCadastroImportacaoIntegracao tipoCadastro,
			IdentificacaoIntegracao integracao);

		Task<List<ImportacaoCadastroIntegracao>> ObterRegistrosPorCategoriaLoja(
			Guid lojaId,
			List<Guid?> listaCategoriaId,
			IdentificacaoIntegracao integracao);

		Task LimparCategoriasEProdutosSelecionados(
			Guid integracaoId);

		Task Limpar(Guid integracaoId);

		Task<int> ObterSequenciaOrdenacaoProduto(
			Guid categoriaId,
			Guid integracaoId);

		Task<int> ObterSequenciaOrdenacaoCategoria(
			Guid integracaoId);
	}
}