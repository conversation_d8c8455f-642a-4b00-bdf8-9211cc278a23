﻿using AutoMapper;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Dtos.Generate.Request;
using Zendar.Business.Consts;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Business.ViewModels.Integracao.Tray;
using Zendar.Business.ViewModels.V1;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;
using Zendar.Data.Resources.Mensagens;
using Zendar.QueueService.Interfaces;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Business.Services.IntegracaoServices.IntegracaoService
{
    public class IntegracaoService : BaseService, IIntegracaoService
    {
        private readonly INotificationHubService _notificationHubService;
        private readonly IServiceBusEnqueueMessage _serviceBusEnqueueMessage;
        private readonly IIntegracaoRepository _integracaoRepository;
        private readonly ITabelaPrecoProdutoCorTamanhoRepository _tabelaPrecoProdutoCorTamanhoRepository;
        private readonly IDocumentoFiscalRepository _documentoFiscalRepository;
        private readonly IOperacaoRepository _operacaoRepository;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IMapper _mapper;
        private readonly ITransacaoNotaFiscalService _transacaoNotaFiscalService;
        private readonly ILogAuditoriaService _logAuditoriaService;

        public IntegracaoService(
            INotificador notificador,
            INotificationHubService notificationHubService,
            IServiceBusEnqueueMessage serviceBusEnqueueMessage,
            IIntegracaoRepository integracaoRepository,
            ITabelaPrecoProdutoCorTamanhoRepository tabelaPrecoProdutoCorTamanhoRepository,
            IDocumentoFiscalRepository documentoFiscalRepository,
            IOperacaoRepository operacaoRepository,
            IAspNetUserInfo aspNetUserInfo,
            IMapper mapper,
            ITransacaoNotaFiscalService transacaoNotaFiscalService,
            ILogAuditoriaService logAuditoriaService) : base(notificador)
        {
            _notificationHubService = notificationHubService;
            _serviceBusEnqueueMessage = serviceBusEnqueueMessage;
            _integracaoRepository = integracaoRepository;
            _tabelaPrecoProdutoCorTamanhoRepository = tabelaPrecoProdutoCorTamanhoRepository;
            _documentoFiscalRepository = documentoFiscalRepository;
            _operacaoRepository = operacaoRepository;
            _aspNetUserInfo = aspNetUserInfo;
            _mapper = mapper;
            _transacaoNotaFiscalService = transacaoNotaFiscalService;
            _logAuditoriaService = logAuditoriaService;
        }

        public async Task NotificarNotaFiscalStatus(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                var transacaoNotaFiscalViewModel = await _transacaoNotaFiscalService.ObterComPredicate(c => c.DocumentoFiscalId == notificacaoViewModel.Id.Value &&
                                                                                                            !c.Concluido);

                if (transacaoNotaFiscalViewModel != null)
                {
                    var jsonSerializerSettings = new JsonSerializerSettings
                    {
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    };

                    transacaoNotaFiscalViewModel.Status = notificacaoViewModel.Status;
                    transacaoNotaFiscalViewModel.DataHoraUltimaAlteracao = DateTime.UtcNow;
                    transacaoNotaFiscalViewModel.Concluido = true;

                    await _transacaoNotaFiscalService.Alterar(transacaoNotaFiscalViewModel);

                    var notificacaoTransacaoNotaFiscalViewModel = new NotificacaoTransacaoNotaFiscalViewModel();
                    notificacaoTransacaoNotaFiscalViewModel.TransacaoId = transacaoNotaFiscalViewModel.TransacaoId;
                    notificacaoTransacaoNotaFiscalViewModel.UsuarioId = notificacaoViewModel.UsuarioId;

                    var documentoFiscal = await _documentoFiscalRepository.FindByKey(notificacaoViewModel.Id.Value);

                    if (documentoFiscal != null &&
                        documentoFiscal.OperacaoId != null)
                        notificacaoTransacaoNotaFiscalViewModel.OperacaoId = documentoFiscal.OperacaoId.Value;

                    var listaTransacaoNotaFiscalViewModel = await _transacaoNotaFiscalService.ObterListaPorId(transacaoNotaFiscalViewModel.TransacaoId);

                    bool unica = listaTransacaoNotaFiscalViewModel?.Count == 1;

                    if (unica)
                    {
                        notificacaoTransacaoNotaFiscalViewModel.Motivo = notificacaoViewModel.Mensagem;

                        if (notificacaoViewModel.Status == StatusFiscal.RETORNO_INDISPONIVEL)
                            notificacaoTransacaoNotaFiscalViewModel.Status = "Retorno indisponível!";
                        else if (notificacaoViewModel.Status == StatusFiscal.REJEITADA)
                            notificacaoTransacaoNotaFiscalViewModel.Status = "Rejeitada!";
                        else if (notificacaoViewModel.Status == StatusFiscal.AUTORIZADA)
                            notificacaoTransacaoNotaFiscalViewModel.Status = "Sucesso!";
                        else if (notificacaoViewModel.Status == StatusFiscal.CANCELADA)
                            notificacaoTransacaoNotaFiscalViewModel.Status = "Cancelada!";
                        else if (notificacaoViewModel.Status == StatusFiscal.USO_DENEGADO)
                            notificacaoTransacaoNotaFiscalViewModel.Status = "Uso Denegado!";

                        string notificacao = JsonConvert.SerializeObject(notificacaoTransacaoNotaFiscalViewModel, jsonSerializerSettings);

                        await _notificationHubService.NotificarComMensagem(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                                           transacaoNotaFiscalViewModel.TransacaoId,
                                                                           "zendar",
                                                                           "notafiscal-status",
                                                                           notificacao);
                    }
                    else
                    {
                        var concluido =
                            listaTransacaoNotaFiscalViewModel?.Where(c => c.Concluido)
                                                             ?.Count() == listaTransacaoNotaFiscalViewModel?.Count();

                        if (concluido)
                        {
                            var todasSucesso = listaTransacaoNotaFiscalViewModel?.Where(c => c.Status == StatusFiscal.AUTORIZADA)
                                                                                ?.Count() == listaTransacaoNotaFiscalViewModel?.Count();

                            if (todasSucesso)
                                notificacaoTransacaoNotaFiscalViewModel.Status = "Sucesso!";
                            else
                            {
                                var quantidadeRetornoIndisponivel = listaTransacaoNotaFiscalViewModel?.Where(c => c.Status == StatusFiscal.RETORNO_INDISPONIVEL)
                                                                                                     ?.Count();

                                var quantidadeRejeitada = listaTransacaoNotaFiscalViewModel?.Where(c => c.Status == StatusFiscal.REJEITADA)
                                                                                           ?.Count();

                                var quantidadeAutorizada = listaTransacaoNotaFiscalViewModel?.Where(c => c.Status == StatusFiscal.AUTORIZADA)
                                                                                            ?.Count();

                                var quantidadeCancelada = listaTransacaoNotaFiscalViewModel?.Where(c => c.Status == StatusFiscal.CANCELADA)
                                                                                           ?.Count();

                                var quantidadeDenegado = listaTransacaoNotaFiscalViewModel?.Where(c => c.Status == StatusFiscal.CANCELADA)
                                                                                           ?.Count();

                                notificacaoTransacaoNotaFiscalViewModel.QuantidadeRetornoIndisponivel = quantidadeRetornoIndisponivel == null ? 0 : quantidadeRetornoIndisponivel.Value;
                                notificacaoTransacaoNotaFiscalViewModel.QuantidadeRejeitada = quantidadeRejeitada == null ? 0 : quantidadeRejeitada.Value;
                                notificacaoTransacaoNotaFiscalViewModel.QuantidadeAutorizada = quantidadeAutorizada == null ? 0 : quantidadeAutorizada.Value;
                                notificacaoTransacaoNotaFiscalViewModel.QuantidadeCancelada = quantidadeCancelada == null ? 0 : quantidadeCancelada.Value;
                                notificacaoTransacaoNotaFiscalViewModel.QuantidadeDenegado = quantidadeDenegado == null ? 0 : quantidadeDenegado.Value;
                            }

                            string notificacao = JsonConvert.SerializeObject(notificacaoTransacaoNotaFiscalViewModel, jsonSerializerSettings);

                            await _notificationHubService.NotificarComMensagem(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                                               transacaoNotaFiscalViewModel.TransacaoId,
                                                                               "zendar",
                                                                               "notafiscal-status",
                                                                               notificacao);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                NotificarAviso(ex.Message);

                return;
            }
        }

        public async Task NotificarNotaFiscalEmitindo(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                await _notificationHubService.NotificarComMensagem(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                                   notificacaoViewModel.Id,
                                                                   "zendar",
                                                                   "notafiscal-emitindo",
                                                                   notificacaoViewModel.Mensagem);
            }
            catch (Exception ex)
            {
                NotificarAviso(ex.Message);

                return;
            }
        }

        public async Task NotificarNotaFiscalAviso(
            NotificacaoViewModel notificacaoViewModel)
        {
            try
            {
                await _notificationHubService.NotificarComMensagem(new Guid(_aspNetUserInfo.LojaId?.ToString()),
                                                                   notificacaoViewModel.Id,
                                                                   "zendar",
                                                                   "notafiscal-aviso",
                                                                   notificacaoViewModel.Mensagem);
            }
            catch (Exception ex)
            {
                NotificarAviso(ex.Message);

                return;
            }
        }

        public async Task GerarNotaFiscal(
            GerarNotaFiscalRequest gerarNotaFiscalRequest,
            Guid transacaoId)
        {
            try
            {
                if (gerarNotaFiscalRequest.TransportadoraId == null ||
                    gerarNotaFiscalRequest.TransportadoraId == Guid.Empty)
                {
                    NotificarAviso("Por favor preencha a Transportadora.");

                    return;
                }
                else if (gerarNotaFiscalRequest.DataSaida == null ||
                         gerarNotaFiscalRequest.DataSaida == DateTime.MinValue)
                {
                    NotificarAviso("Por favor preencha a Data de Saída.");

                    return;
                }
                else if (gerarNotaFiscalRequest.DataEmissao == DateTime.MinValue)
                {
                    NotificarAviso("Por favor preencha a Data de Emissão.");

                    return;
                }
                else if (gerarNotaFiscalRequest.DataSaida.HasValue &&
                         gerarNotaFiscalRequest.DataSaida < gerarNotaFiscalRequest.DataEmissao)
                {
                    NotificarAviso("A Data de Saída deve ser maior ou igual a Data de Emissão.");

                    return;
                }
                else if (gerarNotaFiscalRequest.ListaOperacaoId == null ||
                         gerarNotaFiscalRequest.ListaOperacaoId?.Count == 0)
                {
                    NotificarAviso("Por favor selecione uma Nota Fiscal.");

                    return;
                }

                foreach (var operacaoId in gerarNotaFiscalRequest.ListaOperacaoId)
                {
                    var operacao = await _operacaoRepository.FindByKey(operacaoId);

                    if (operacao.Status == StatusOperacao.CANCELADA)
                    {
                        NotificarAviso("Não é possivel emitir uma nota fiscal para uma operação cancelada.");

                        return;
                    }
                }

                gerarNotaFiscalRequest.HTTP_REFERER_MULTIEMPRESA = _aspNetUserInfo?.HostUrl;
                gerarNotaFiscalRequest.LojaId = _aspNetUserInfo?.LojaId?.ToString();
                gerarNotaFiscalRequest.UsuarioId = Guid.Parse(_aspNetUserInfo?.Id?.ToString());
                gerarNotaFiscalRequest.TransacaoId = transacaoId;

                _serviceBusEnqueueMessage.Send(SystemConst.QUEUE_ZENDAR_INVOICE,
                                               JsonConvert.SerializeObject(gerarNotaFiscalRequest));
            }
            catch (Exception ex)
            {
                NotificarAviso(ResourceMensagem.IntegracaoTrayService_FalhaExportarCadastroProduto);

                return;
            }
        }

        public async Task<IntegracaoObterViewModel> ObterPorId(
            Guid id)
        {
            var integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.Id == id);

            return _mapper.Map<IntegracaoObterViewModel>(integracao);
        }

        public async Task<IntegracaoObterViewModel> Obter(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid? lojaId = null)
        {
            if (!lojaId.HasValue)
                lojaId = _aspNetUserInfo.LojaId;

            Data.Models.Aplicacao.Integracao integracao = null;

            if (lojaId == Guid.Empty)
                integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == identificacaoIntegracao);
            else
            {
                integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == identificacaoIntegracao &&
                                                                                         i.LojaId == lojaId.Value);
                if (integracao == null)
                {
                    NotificarAvisoRegistroNaoEncontrada("integração");

                    return null;
                }
            }

            return _mapper.Map<IntegracaoObterViewModel>(integracao);
        }

        public async Task<IntegracaoObterViewModel> ObterIntegracaoSTI3PAY()
        {
            Guid? lojaId = _aspNetUserInfo.LojaId;

            var integracao = await _integracaoRepository
                .FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.STI3PAY
                                              && i.LojaId == lojaId.Value);

            return _mapper.Map<IntegracaoObterViewModel>(integracao);
        }

        public async Task<bool> LojaPossuiIntegracao(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId)
        {
            return await _integracaoRepository.ValidarModulo(lojaId, identificacaoIntegracao);
        }

        public async Task<List<IntegracaoObterViewModel>> ListarIntegracaoPorIdentificador(
            IdentificacaoIntegracao identificacaoIntegracao)
        {
            var listaIntegracoes = await _integracaoRepository.ListarIntegracaoPorIdentificador(identificacaoIntegracao);

            return _mapper.Map<List<IntegracaoObterViewModel>>(listaIntegracoes);
        }

        public async Task<IEnumerable<Guid>> ListarIntegracaoConfiguradaPorIdentificador(IdentificacaoIntegracao identificacaoIntegracao)
        {
            return await _integracaoRepository.ListarIntegracaoConfiguradaPorIdentificador(identificacaoIntegracao);
        }

        public async Task<Guid?> ObterId(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId)
        {
            var integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == identificacaoIntegracao &&
                                                                                         i.LojaId == lojaId,
                                                                                         i => new Data.Models.Aplicacao.Integracao { Id = i.Id });
            if (integracao == null)
            {
                NotificarAvisoRegistroNaoEncontrada("integração");

                return null;
            }

            return integracao.Id;
        }

        public async Task<Guid?> ObterIdLocalEstoque(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId)
        {
            var integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == identificacaoIntegracao &&
                                                                                         i.LojaId == lojaId,
                                                                                         i => new Data.Models.Aplicacao.Integracao { LocalEstoqueId = i.LocalEstoqueId });

            return integracao?.LocalEstoqueId;
        }

        public async Task<InformacoesIntegracaoParaGerarOperacaoViewModel> ObterInformacoesParaCriarOperacao(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId)
        {
            var integracao = await _integracaoRepository.FirstOrDefaultAsNoTracking(i => i.IdentificacaoIntegracao == identificacaoIntegracao &&
                                                                                         i.LojaId == lojaId,
                                                                                    i => new Data.Models.Aplicacao.Integracao
                                                                                    {
                                                                                        Id = i.Id,
                                                                                        UsuarioId = i.UsuarioId,
                                                                                        VendedorId = i.VendedorId,
                                                                                        LocalEstoqueId = i.LocalEstoqueId,
                                                                                        TabelaPrecoId = i.TabelaPrecoId,
                                                                                        LancarVendasNoCaixa = i.LancarVendasNoCaixa
                                                                                    });

            if (integracao == null)
            {
                NotificarAvisoRegistroNaoEncontrada("integração");

                return null;
            }

            return new InformacoesIntegracaoParaGerarOperacaoViewModel
            {
                Id = integracao.Id,
                UsuarioId = integracao.UsuarioId,
                VendedorId = integracao.VendedorId,
                LocalEstoqueId = integracao.LocalEstoqueId,
                TabelaPrecoId = integracao.TabelaPrecoId,
                LancarVendasNoCaixa = integracao.LancarVendasNoCaixa
            };
        }

        public async Task<Guid> Cadastrar(
            IntegracaoViewModel cadastroViewModel,
            string configuracoes)
        {
            IdentificacaoIntegracao[] integracoesComSincronizacaoPreHabilitada = new[] {
                IdentificacaoIntegracao.CAIXA_PDV_AUTONOMO,
                IdentificacaoIntegracao.FRENTE_CAIXA,
                IdentificacaoIntegracao.CAIXA_MOVEL,
                IdentificacaoIntegracao.AUTO_ATENDIMENTO,
            };

            var integracao = _mapper.Map<Data.Models.Aplicacao.Integracao>(cadastroViewModel);
            integracao.LojaId = _aspNetUserInfo.LojaId.Value;
            integracao.UsuarioId = cadastroViewModel.UsuarioId;
            integracao.IdentificacaoIntegracao = cadastroViewModel.IdentificacaoIntegracao;
            integracao.Ativo = true;
            integracao.DataAtivacao = DateTime.UtcNow;
            integracao.Configuracoes = configuracoes;
            integracao.LancarVendasNoCaixa = true;
            integracao.SincronizacaoHabilitada = integracoesComSincronizacaoPreHabilitada.Contains(cadastroViewModel.IdentificacaoIntegracao);

            // Origem padrão
            var integracaoOrigemPadrao = ObterIntegracaoOrigemPadraoPorIdentificacaoIntegracao(integracao.IdentificacaoIntegracao);

            if (integracaoOrigemPadrao != null)
            {
                integracao.IntegracaoOrigensPedido = new List<IntegracaoOrigemPedido>
            {
                integracaoOrigemPadrao
            };
            }

            await _integracaoRepository.Insert(integracao);

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
            {
                Tela = LogAuditoriaTela.INTEGRACAO,
                Operacao = LogAuditoriaOperacao.CADASTRAR,
                Descricao = $"Integração: {integracao.IdentificacaoIntegracao}"
            });

            return integracao.Id;
        }

        public async Task Alterar(
            IntegracaoViewModel cadastroViewModel,
            string configuracoes)
        {
            var integracao = await _integracaoRepository.FindByKey(cadastroViewModel.Id);
            if (integracao == null)
            {
                NotificarAvisoRegistroNaoEncontrada("integração");
                return;
            }

            integracao.Configuracoes = configuracoes;
            integracao.SincronizacaoHabilitada = cadastroViewModel.SincronizacaoHabilitada;
            integracao.LocalEstoqueId = cadastroViewModel.LocalEstoqueId;
            integracao.Ativo = cadastroViewModel.Ativo;

            await _integracaoRepository.SaveChanges();

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
            {
                Tela = LogAuditoriaTela.INTEGRACAO,
                Operacao = LogAuditoriaOperacao.ALTERAR,
                Descricao = $"Integração: {integracao.IdentificacaoIntegracao}"
            });
        }

        public async Task AlterarVendedor(
            Guid integracaoId,
            Guid vendedorId)
        {
            try
            {
                var integracao = await _integracaoRepository.FindByKey(integracaoId);
                if (integracao == null) return;

                integracao.VendedorId = vendedorId;

                await _integracaoRepository.SaveChanges();
            }
            catch (Exception ex)
            {

            }
        }

        public async Task AlterarTabelaPreco(
            Guid integracaoId,
            Guid tabelaPrecoId)
        {
            var integracao = await _integracaoRepository.FindByKey(integracaoId);
            if (integracao == null) return;

            integracao.TabelaPrecoId = tabelaPrecoId;

            await _integracaoRepository.SaveChanges();

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel
            {
                Tela = LogAuditoriaTela.INTEGRACAO,
                Operacao = LogAuditoriaOperacao.ALTERAR,
                Descricao = $"Integração: {integracao.IdentificacaoIntegracao}. Tabela de preço alterada."
            });
        }

        public async Task AlterarPromocao(
            Guid integracaoId,
            Guid? promocaoId)
        {
            var integracao = await _integracaoRepository.FindByKey(integracaoId);
            if (integracao == null) return;

            var configuracoes = JsonConvert.DeserializeObject<ConfiguracaoIntegracaoViewModel>(integracao.Configuracoes);

            integracao.PromocaoId = promocaoId;
            configuracoes.AtualizacaoPromocao = promocaoId.HasValue;
            integracao.Configuracoes = JsonConvert.SerializeObject(configuracoes);

            await _integracaoRepository.SaveChanges();
        }

        public async Task AlterarLocalEstoque(
            Guid integracaoId,
            Guid localEstoqueId)
        {
            var integracao = await _integracaoRepository.FindByKey(integracaoId);
            if (integracao == null) return;

            integracao.LocalEstoqueId = localEstoqueId;

            await _integracaoRepository.SaveChanges();
        }

        public async Task AlterarPropriedadeDinamica<T>(
            Guid integracaoId,
            string prop,
            dynamic value) where T : class
        {
            var integracao = await _integracaoRepository.FindByKey(integracaoId);

            if (integracao == null) return;

            var configuracoes = JsonConvert.DeserializeObject<T>(integracao.Configuracoes);

            typeof(T).GetProperty(prop).SetValue(configuracoes, value);

            integracao.Configuracoes = JsonConvert.SerializeObject(configuracoes);

            await _integracaoRepository.SaveChanges();
        }

        public async Task Ativar(Guid integracaoId)
        {
            var integracao = await _integracaoRepository.FirstOrDefault(f => f.Id == integracaoId);

            if (integracao is null)
            {
                NotificarAvisoRegistroNaoEncontrada("integração");
                return;
            }

            integracao.Ativo = true;

            await _integracaoRepository.SaveChanges();
        }

        public async Task<bool> VerificarSeExisteIntegracao(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid? lojaId = null)
        {
            if (!lojaId.HasValue)
                lojaId = _aspNetUserInfo.LojaId;

            return await _integracaoRepository.Any(i => i.IdentificacaoIntegracao == identificacaoIntegracao &&
                                                        i.LojaId == lojaId);
        }

        private IntegracaoOrigemPedido ObterIntegracaoOrigemPadraoPorIdentificacaoIntegracao(
            IdentificacaoIntegracao identificacaoIntegracao)
        {
            if (identificacaoIntegracao == IdentificacaoIntegracao.TRAY)
            {
                return new IntegracaoOrigemPedido()
                {
                    Descricao = identificacaoIntegracao.ObterDescricao(),
                    DescricaoPlataforma = SystemConst.ZENDAR_SYNC_TRAY_ORIGEM_PADRAO,
                    Cnpj = SystemConst.ZENDAR_SYNC_TRAY_CNPJ,
                    Padrao = true
                };
            }

            return null;
        }

        public void Dispose()
        {

        }
    }
}
