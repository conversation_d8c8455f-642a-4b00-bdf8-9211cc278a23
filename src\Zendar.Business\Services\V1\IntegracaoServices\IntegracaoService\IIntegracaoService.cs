﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Dtos.Generate.Request;
using Zendar.Business.ViewModels.Integracao;
using Zendar.Data.Enums.Integracao;

namespace Zendar.Business.Services.IntegracaoServices.IntegracaoService
{
    public interface IIntegracaoService : IDisposable
    {
        Task NotificarNotaFiscalStatus(
            NotificacaoViewModel notificacaoViewModel);

        Task NotificarNotaFiscalEmitindo(
           NotificacaoViewModel notificacaoViewModel);

        Task NotificarNotaFiscalAviso(
            NotificacaoViewModel notificacaoViewModel);

        Task GerarNotaFiscal(
            GerarNotaFiscalRequest gerarNotaFiscal,
            Guid transacaoId);

        Task<bool> LojaPossuiIntegracao(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId);

        Task<IntegracaoObterViewModel> ObterPorId(
            Guid id);

        Task<IntegracaoObterViewModel> Obter(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid? lojaId = null);

        Task<IntegracaoObterViewModel> ObterIntegracaoSTI3PAY();

        Task<Guid?> ObterId(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId);

        Task<InformacoesIntegracaoParaGerarOperacaoViewModel> ObterInformacoesParaCriarOperacao(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId);

        Task<Guid?> ObterIdLocalEstoque(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid lojaId);

        Task<Guid> Cadastrar(
            IntegracaoViewModel cadastroViewModel,
            string configuracoes);

        Task Alterar(
            IntegracaoViewModel cadastroViewModel,
            string configuracoes);

        Task AlterarVendedor(
            Guid integracaoId,
            Guid vendedorId);

        Task AlterarTabelaPreco(
            Guid integracaoId,
            Guid tabelaPrecoId);

        Task AlterarPromocao(
            Guid integracaoId,
            Guid? promocaoId);

        Task AlterarLocalEstoque(
            Guid integracaoId,
            Guid localEstoqueId);

        Task AlterarPropriedadeDinamica<T>
            (Guid integracaoId,
            string prop,
            dynamic value) where T : class;

        Task Ativar(Guid integracaoId);

        Task<bool> VerificarSeExisteIntegracao(
            IdentificacaoIntegracao identificacaoIntegracao,
            Guid? lojaId = null);

        Task<IEnumerable<Guid>> ListarIntegracaoConfiguradaPorIdentificador(IdentificacaoIntegracao identificacaoIntegracao);

        Task<List<IntegracaoObterViewModel>> ListarIntegracaoPorIdentificador(
            IdentificacaoIntegracao identificacaoIntegracao);
    }
}
