﻿using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Zendar.Business.Helpers;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;

namespace Zendar.Business.Services
{
    public class CacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IAspNetUserInfo _aspNetUserInfo;


        public CacheService(IDistributedCache distributedCache,
                            IAspNetUserInfo aspNetUserInfo)
        {
            _distributedCache = distributedCache;
            _aspNetUserInfo = aspNetUserInfo;
        }

        public async Task AdicionarAsync(CacheModel cacheModel, bool utilizarHostChave = true)
        {
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpiration = cacheModel.ExpirarEm,
                SlidingExpiration = cacheModel.ExpirarAposInatividade
            };

            await _distributedCache.SetStringAsync(
                ObterChave(cacheModel.Chave, utilizarHostChave),
                cacheModel.ValorJson,
                options);
        }

        public async Task RemoverAsync(string chave, bool utilizarHostChave = true)
        {
            if (!string.IsNullOrEmpty(chave))
            {
                await _distributedCache.RemoveAsync(ObterChave(chave, utilizarHostChave));
            }
        }

        public async Task<string> ObterAsync(string chave, bool utilizarHostChave = true)
        {
            if (!string.IsNullOrEmpty(chave))
            {
                return await _distributedCache.GetStringAsync(ObterChave(chave, utilizarHostChave));
            }

            return null;
        }

        public async Task<T> ObterAsync<T>(string chave, bool utilizarHostChave = true) where T : new()
        {
            if (!string.IsNullOrEmpty(chave))
            {
                var valor = await _distributedCache.GetStringAsync(ObterChave(chave, utilizarHostChave));

                if (!string.IsNullOrEmpty(valor))
                {
                    return JsonConvert.DeserializeObject<T>(valor);
                }
            }

            return default;
        }

        public async Task<bool> Existe(string chave, bool utilizarHostChave = true)
        {
            if (!string.IsNullOrEmpty(chave))
            {
                var valor = await _distributedCache.GetStringAsync(ObterChave(chave, utilizarHostChave));

                if (!string.IsNullOrEmpty(valor))
                {
                    return true;
                }
            }

            return false;
        }

        private string ObterChave(string chave, bool utilizarHostChave)
        {
            return utilizarHostChave ? $"{_aspNetUserInfo.HostUrl}_{chave.ToUpper()}" : chave.ToUpper();
        }
    }
}
