{"ConnectionStrings": {"DefaultConnection": "Data Source=tcp:zendar-dev.database.windows.net,1433;Initial Catalog={0};User Id=zendar-dev-admin;Password=***************;MultipleActiveResultSets=true;", "MultiempresaConnection": "Data Source=tcp:zendar-dev.database.windows.net,1433;Initial Catalog=multiempresa;User Id=zendar-dev-admin;Password=***************;MultipleActiveResultSets=true;", "ServiceBus": "Endpoint=sb://sb-zendar-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=5XClu79yk4mibM88xXcFNbXw5kTh298mw+ASbIyTRsg=", "TableStorage": "DefaultEndpointsProtocol=https;AccountName=workerzendardev;AccountKey=****************************************************************************************;BlobEndpoint=https://workerzendardev.blob.core.windows.net/;QueueEndpoint=https://workerzendardev.queue.core.windows.net/;TableEndpoint=https://workerzendardev.table.core.windows.net/;FileEndpoint=https://workerzendardev.file.core.windows.net/;"}, "StorageSettings": {"ArquivosFiscais": "DefaultEndpointsProtocol=https;AccountName=zendararquivosfiscaisdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararquivosfiscaisdev.blob.core.windows.net/;QueueEndpoint=https://zendararquivosfiscaisdev.queue.core.windows.net/;TableEndpoint=https://zendararquivosfiscaisdev.table.core.windows.net/;FileEndpoint=https://zendararquivosfiscaisdev.file.core.windows.net/;"}, "WebHooks": {"Teams": "https://sti3combr.webhook.office.com/webhookb2/1dd76236-2fb0-44a9-8615-6b2ff19f5f4d@7518d316-dcf1-4341-b8d3-b3f9e5fe9b38/IncomingWebhook/baeb228d4a744e56a964b0db53e60ca7/22d9cabb-fa85-4e30-9b25-7bc38c171b1b"}, "EmailSettings": {"Host": "smtp.office365.com", "Port": "587", "EmailZendar": "<EMAIL>", "EmailPowerstock": "<EMAIL>", "Password": "@SistemasCloudSTi32022$", "EmailsLogErro": "<EMAIL>"}, "ZendarSyncApi": {"TrayUrl": "https://zendar-sync-tray-dev-api.azurewebsites.net/api", "PdvAutonomoUrl": "https://zendar-sync-pdv-dev-api.azurewebsites.net/api", "FrenteCaixaUrl": "https://zendar-sync-frente-caixa-dev-api.azurewebsites.net/api"}}