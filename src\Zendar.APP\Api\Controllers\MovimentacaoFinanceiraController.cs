﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.APP.Constants;
using Zendar.APP.Extensions;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraServices;
using Zendar.Business.Services.Financeiro.MovimentacaoFinanceiraValidacaoServices;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Operacao;

namespace Zendar.APP.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MovimentacaoFinanceiraController : MainController
    {
        private readonly IMovimentacaoFinanceiraService _movimentacaoFinanceiraService;
        private readonly IMovimentacaoFinanceiraValidacoesService _movimentacaoFinanceiraValidacoesService;

        public MovimentacaoFinanceiraController(INotificador notificador,
                                                IMovimentacaoFinanceiraService movimentacaoFinanceiraService,
                                                IMovimentacaoFinanceiraValidacoesService movimentacaoFinanceiraValidacoesService) : base(notificador)
        {
            _movimentacaoFinanceiraService = movimentacaoFinanceiraService;
            _movimentacaoFinanceiraValidacoesService = movimentacaoFinanceiraValidacoesService;
        }

        [HttpPost(Endpoints.Cadastrar)]
        [ClaimsAuthorize()]
        public async Task<ActionResult> Cadastrar(MovimentacaoFinanceiraInserirViewModel movimentacaoFinanceiraViewModel)
        {
            try
            {
                await _movimentacaoFinanceiraService.Cadastrar(movimentacaoFinanceiraViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
            }
            return CustomResponse();
        }

        [HttpPut(Endpoints.AlterarValorVencimento)]
        [ClaimsAuthorize()]
        public async Task<ActionResult> AlterarValorVencimento(List<MovimentacaoFinanceiraAlterarViewModel> movimentacaoFinanceiraViewModel)
        {
            try
            {
                await _movimentacaoFinanceiraService.AlterarValorEVencimento(movimentacaoFinanceiraViewModel);
            }
            catch (Exception ex)
            {
                NotificarErro(ex, movimentacaoFinanceiraViewModel);
            }
            return CustomResponse();
        }

		[HttpPut(Endpoints.AlterarInformacoesFiscais)]
		[ClaimsAuthorize()]
		public async Task<ActionResult> AlterarInformacoesFiscais(
            [FromRoute] Guid identificadorAgrupamento,
            [FromBody] InformacaoFiscalPagamentoViewModel informacaoFiscal)
		{
			try
			{
				await _movimentacaoFinanceiraService.AlterarInformacoesFiscaisPagamento(identificadorAgrupamento, informacaoFiscal);
			}
			catch (Exception ex)
			{
				NotificarErro(ex, identificadorAgrupamento, informacaoFiscal);
			}
			return CustomResponse();
		}

		[HttpDelete(Endpoints.Excluir)]
        [ClaimsAuthorize()]
        public async Task<ActionResult> Excluir(Guid? identificadorAgrupamento, Guid? operacaoId)
        {
            try
            {
                await _movimentacaoFinanceiraService.Excluir(identificadorAgrupamento, operacaoId);
                return CustomResponse();
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ValidarContasEmAberto)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<bool>> ValidarContasEmAberto(Guid clienteId)
        {
            try
            {
                return CustomResponse(await _movimentacaoFinanceiraValidacoesService.ValidarContasEmAberto(clienteId));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ValidarLimiteDeCredito)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<bool>> ValidarLimiteDeCredito(Guid clienteId, decimal valor)
        {
            try
            {
                return CustomResponse(await _movimentacaoFinanceiraValidacoesService.ValidarLimiteDeCredito(clienteId, valor));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }

        [HttpGet(Endpoints.ObterParcelas)]
        [ClaimsAuthorize()]
        public async Task<ActionResult<List<OperacaoMovimentacaoFinanceiraParcelaObterViewModel>>> ObterParcelas(Guid identificadorAgrupamento)
        {
            try
            {
                return CustomResponse(await _movimentacaoFinanceiraService.ObterParcelas(identificadorAgrupamento));
            }
            catch (Exception ex)
            {
                NotificarErro(ex);
                return CustomResponse();
            }
        }
    }
}
