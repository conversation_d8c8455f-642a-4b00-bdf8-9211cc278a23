﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;

namespace Zendar.Data.Repository.Aplicacao
{
    public class ImportacaoCadastroIntegracaoRepository : RepositoryAplicacao<ImportacaoCadastroIntegracao>, IImportacaoCadastroIntegracaoRepository
    {
        public ImportacaoCadastroIntegracaoRepository(AplicacaoContexto db) : base(db)
        {
        }

        public async Task<List<ImportacaoCadastroIntegracao>> ObterRegistrosPorLoja(
            Guid lojaId,
            TipoCadastroImportacaoIntegracao tipoCadastro,
			IdentificacaoIntegracao integracao)
        {
            return await DbSet.Where(x => x.LojaId == lojaId && 
                                          x.TipoCadastro == tipoCadastro &&
                                          x.Integracao.IdentificacaoIntegracao == IdentificacaoIntegracao.CAIXA_MOVEL)
                              .ToListAsync();
        }

        public async Task<List<ImportacaoCadastroIntegracao>> ObterRegistrosPorCategoriaLoja(
            Guid lojaId,
            List<Guid?> listaCategoriaId,
            IdentificacaoIntegracao integracao)
        {
            var listaRegistros = new List<ImportacaoCadastroIntegracao>();

            foreach (var categoriaId in listaCategoriaId)
            {
                var registros = await DbSet
                              .Where(x => x.LojaId == lojaId &&
                                          x.CategoriaProdutoId == categoriaId &&
                                          x.TipoCadastro == TipoCadastroImportacaoIntegracao.PRODUTO &&
                                          x.Integracao.IdentificacaoIntegracao == IdentificacaoIntegracao.CAIXA_MOVEL)
                              .ToListAsync();
                
                listaRegistros.AddRange(registros);
            }

            return listaRegistros;
        }

		public async Task LimparCategoriasEProdutosSelecionados(
			Guid integracaoId)
		{
			var sql = $"DELETE FROM {nameof(ImportacaoCadastroIntegracao)} WHERE IntegracaoId = '{integracaoId}' AND TipoCadastro IN ('{nameof(TipoCadastroImportacaoIntegracao.CATEGORIA)}', '{nameof(TipoCadastroImportacaoIntegracao.PRODUTO)}');";

			await Db.Database.ExecuteSqlRawAsync(sql);
		}

		public async Task Limpar(Guid integracaoId)
		{
			var sql = $"DELETE FROM {nameof(ImportacaoCadastroIntegracao)} WHERE IntegracaoId = '{integracaoId}';";

			await Db.Database.ExecuteSqlRawAsync(sql);
		}

		public async Task<int> ObterSequenciaOrdenacaoProduto(
			Guid categoriaId,
			Guid integracaoId)
		{
			return await DbSet
				.Where(x => x.IntegracaoId == integracaoId &&
							x.CategoriaProdutoId == categoriaId &&
							x.TipoCadastro == TipoCadastroImportacaoIntegracao.PRODUTO)
				.MaxAsync(x => x.SequenciaOrdenacao);
		}

		public async Task<int> ObterSequenciaOrdenacaoCategoria(
			Guid integracaoId)
		{
			return await DbSet
				.Where(x => x.IntegracaoId == integracaoId &&
							x.TipoCadastro == TipoCadastroImportacaoIntegracao.CATEGORIA)
				.MaxAsync(x => x.SequenciaOrdenacao);
		}
    }
}
