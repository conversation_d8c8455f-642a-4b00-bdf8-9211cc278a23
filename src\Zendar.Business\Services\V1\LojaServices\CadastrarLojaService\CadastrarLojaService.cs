﻿using AutoMapper;
using Hangfire;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers;
using Multiempresa.Shared.Interfaces.Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Emails;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.Fiscal;
using Zendar.Business.Services.FormaPagamentoRecebimentoService;
using Zendar.Business.Services.UsuarioServices.CadastrarUsuarioService;
using Zendar.Business.Validations.Models;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Loja;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Business.Services.LojaServices.CadastrarLojaService
{
    public class CadastrarLojaService : BaseService, ICadastrarLojaService
    {
        private readonly ILojaRepository _lojaRepository;
        private readonly ILojaUsuarioRepository _lojaUsuarioRepository;
        private readonly IFormaPagamentoRecebimentoLojaRepository _formaPagamentoRecebimentoLojaRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IUsuarioService _usuarioService;
        private readonly IVendedorService _vendedorService;
        private readonly IRegraFiscalService _regraFiscalService;
        private readonly ITabelaPrecoService _tabelaPrecoService;
        private readonly IDashboardInicialService _dashboardInicialService;
        private readonly ILocalEstoqueService _localEstoqueService;
        private readonly ICadastrarUsuarioService _cadastrarUsuarioService;
        private readonly IFormaPagamentoRecebimentoService _formaPagamentoRecebimentoService;

        private readonly IMapper _mapper;
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IAspNetUserInfo _aspNetUserInfo;

        public CadastrarLojaService(INotificador notificador,
                                    ILojaRepository lojaRepository,
                                    IMapper mapper,
                                    IUsuarioService usuarioService,
                                    IRegraFiscalService regraFiscalService,
                                    ITabelaPrecoService tabelaPrecoService,
                                    IDatabaseTransaction databaseTransaction,
                                    IDashboardInicialService dashboardInicialService,
                                    IVendedorService vendedorService,
                                    ILocalEstoqueService localEstoqueService,
                                    ILojaUsuarioRepository lojaUsuarioRepository,
                                    ILogAuditoriaService logAuditoriaService,
                                    IAspNetUserInfo aspNetUserInfo,
                                    ICadastrarUsuarioService cadastrarUsuarioService,
                                    IFormaPagamentoRecebimentoService formaPagamentoRecebimentoService,
                                    IFormaPagamentoRecebimentoLojaRepository formaPagamentoRecebimentoLojaRepository,
                                    IUsuarioRepository usuarioRepository) : base(notificador)
        {
            _lojaRepository = lojaRepository;
            _mapper = mapper;
            _usuarioService = usuarioService;
            _regraFiscalService = regraFiscalService;
            _tabelaPrecoService = tabelaPrecoService;
            _databaseTransaction = databaseTransaction;
            _dashboardInicialService = dashboardInicialService;
            _vendedorService = vendedorService;
            _localEstoqueService = localEstoqueService;
            _lojaUsuarioRepository = lojaUsuarioRepository;
            _logAuditoriaService = logAuditoriaService;
            _aspNetUserInfo = aspNetUserInfo;
            _cadastrarUsuarioService = cadastrarUsuarioService;
            _formaPagamentoRecebimentoService = formaPagamentoRecebimentoService;
            _formaPagamentoRecebimentoLojaRepository = formaPagamentoRecebimentoLojaRepository;
            _usuarioRepository = usuarioRepository;
        }

        public async Task Cadastrar(LojaCadastroViewModel lojaViewModel)
        {
            var loja = _mapper.Map<Loja>(lojaViewModel);
            var lojaBasic = lojaViewModel.Servicos.Any(p => p.ReferenciaServico.Equals(ReferenciaServicoStargate.PLANO_BASIC));

            if (!ExecutarValidacao(new LojaValidation(), loja)) return;

            loja.DataHoraCadastro = DateTime.UtcNow;
            loja.DataHoraUltimaAlteracao = DateTime.UtcNow;
            loja.LojaMultaJuros = CriarLojaMultaJuros();
            loja.LojaImpressaoRelatorio = CriarLojaImpressaoRelatorio();
            loja.LojaFiscal = await CriarLojaFiscal(lojaBasic);
            loja.ContasFinanceiras = CriarContaFinanceiraCofreBancaria();
            loja.LocaisEstoque = CriarLocalEstoquePadrao();
            loja.TabelaPrecoLojas = await VincularComTabelaPrecoPadrao();
            loja.ProdutoPrecoLojas = await GerarProdutoPrecoLoja(lojaViewModel.IdAssinaturaBasePrecos);

            loja.LojaServicos = lojaViewModel.Servicos.Select(s => new LojaServicos
            {
                ReferenciaServico = s.ReferenciaServico,
                TipoServico = s.TipoServico,
                DataBloqueio = s.DataBloqueio,
                Quantidade = s.Quantidade
            }).ToList();

            _databaseTransaction.BeginTransaction();

            await _lojaRepository.Insert(loja);

            await VincularComUsuariosPadrao(loja.Id);

            if (!await VincularFormasRecebimento(loja))
            {
                NotificarAviso("Não foi possível vincular as formas de recebimento com a loja.");
                return;
            }

            await _dashboardInicialService.CriarEstruturaInicial(loja.Id);

            _databaseTransaction.Commit();

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.LOJA, LogAuditoriaOperacao.CADASTRAR, $"Nome: {loja.RazaoSocial}"));

            var administrador = await _usuarioService.ObterAdministrador();

            if (administrador != null)
            {
                var emailEnvio = new EmailEnvio
                {
                    Sistema = _aspNetUserInfo.Sistema.ObterDescricao(),
                    Emails = new List<string> { administrador.Email },
                    Assunto = "Nova loja confirmada!",
                    Corpo = EmailNovaLoja.MontarEmail(administrador.Nome, _aspNetUserInfo.HostUrl, _aspNetUserInfo.Sistema.ObterDescricao())
                };

                BackgroundJob.Enqueue<IEmailService>(x => x.Enviar(emailEnvio));
            }
        }

        private LojaMultaJuros CriarLojaMultaJuros()
        {
            return new LojaMultaJuros
            {
                DataHoraCadastro = DateTime.UtcNow,
                DataHoraUltimaAlteracao = DateTime.UtcNow
            };
        }

        private LojaImpressaoRelatorio CriarLojaImpressaoRelatorio()
        {
            return new LojaImpressaoRelatorio
            {
                DataHoraCadastro = DateTime.UtcNow,
                DataHoraUltimaAlteracao = DateTime.UtcNow
            };
        }

        private async Task<LojaFiscal> CriarLojaFiscal(bool planoBasic = false)
        {
            var regraFiscalId = planoBasic
                                ? (Guid)await _regraFiscalService.ObterRegraTributado()
                                : await _regraFiscalService.ObterPrimeiraRegraFiscal();

            return new LojaFiscal
            {
                RegraFiscalPadraoId = regraFiscalId,
                ContabilistaCidadeId = 5300,
                ContabilistaPaisId = 1,
                RegimeTributario = RegimeTributario.SIMPLES_NACIONAL,
                TipoAmbienteFiscal = planoBasic ? AmbienteFiscal.HOMOLOGACAO : AmbienteFiscal.PRODUCAO,
                DataHoraCadastro = DateTime.UtcNow,
                DataHoraUltimaAlteracao = DateTime.UtcNow,
                BeneficiarioPagamento = false,
            };
        }

        private List<ContaFinanceira> CriarContaFinanceiraCofreBancaria()
        {
            return new List<ContaFinanceira>
            {
                new ContaFinanceira
                {
                    Nome = "Conta Cofre",
                    TipoContaFinanceira = TipoContaFinanceira.CONTA_COFRE,
                    PadraoSistema = true,
                    Ativo = true,
                    DataHoraCadastro = DateTime.UtcNow,
                    DataHoraUltimaAlteracao = DateTime.UtcNow
                },
                new ContaFinanceira
                {
                    Nome = "Conta Bancária",
                    TipoContaFinanceira = TipoContaFinanceira.BANCO,
                    PadraoSistema = false,
                    Ativo = true,
                    DataHoraCadastro = DateTime.UtcNow,
                    DataHoraUltimaAlteracao = DateTime.UtcNow
                },
            };
        }

        private List<LocalEstoque> CriarLocalEstoquePadrao()
        {
            return new List<LocalEstoque>
            {
                new LocalEstoque
                {
                    Nome = "Padrão",
                    PadraoSistema = true,
                    Ativo = true,
                    DataHoraCadastro = DateTime.UtcNow,
                    DataHoraUltimaAlteracao = DateTime.UtcNow
                }
            };
        }

        private async Task<List<TabelaPrecoLoja>> VincularComTabelaPrecoPadrao()
        {
            var tabelaPrecoId = await _tabelaPrecoService.ObterTabelaPrecoPadrao();
            return new List<TabelaPrecoLoja>
            {
                new TabelaPrecoLoja { Padrao = true, TabelaPrecoId = tabelaPrecoId }
            };
        }

        private async Task<List<ProdutoPrecoLoja>> GerarProdutoPrecoLoja(Guid? idAssinaturaBasePrecos)
        {
            if (!idAssinaturaBasePrecos.HasValue || idAssinaturaBasePrecos.Value == default)
            {
                return new();
            }

            var lojaBase = await _lojaRepository.FirstOrDefaultAsNoTracking(l => l.AssinaturaId.Equals(idAssinaturaBasePrecos.Value), l => new Loja
            {
                ProdutoPrecoLojas = l.ProdutoPrecoLojas.ToList()
            });

            if (lojaBase == null || lojaBase.ProdutoPrecoLojas == null || !lojaBase.ProdutoPrecoLojas.Any()) return new();

            return lojaBase.ProdutoPrecoLojas.Select(pcl => new ProdutoPrecoLoja
            {
                ProdutoId = pcl.ProdutoId,
                Markup = pcl.Markup,
                PrecoCompra = pcl.PrecoCompra,
                PrecoCusto = pcl.PrecoCusto,
                PrecoVenda = pcl.PrecoVenda,
            }).ToList();
        }

        private async Task VincularComUsuariosPadrao(Guid lojaId)
        {
            var usuarios = await _usuarioRepository.ObterUsuariosParaVincularComLojaCadastrada();

            var vendedorPadraoId = await _vendedorService.ObterVendedorPadrao();
            var localEstoquePadraoId = await _localEstoqueService.ObterPadraoSistema(lojaId);

            await _lojaUsuarioRepository.InsertRange(usuarios
                .Select(usuarioId => new LojaUsuario
			    {
				    LojaId = lojaId,
				    UsuarioId = usuarioId,
				    VendedorId = vendedorPadraoId.Id,
				    LocalEstoqueId = localEstoquePadraoId.Value,
				    LojaPadrao = false
			    }));
        }

        /// <summary>
        /// Vincula as formas de recebimento na loja com a conta bancária.
        /// </summary>
        /// <param name="loja">Loja com pelo menos uma conta bancária</param>
        /// <returns></returns>
        private async Task<bool> VincularFormasRecebimento(Loja loja)
        {
            var contaBancaria = loja.ContasFinanceiras.FirstOrDefault(c => c.TipoContaFinanceira == TipoContaFinanceira.BANCO);
            if (contaBancaria is null) return false;

            var formasRecebimento = await _formaPagamentoRecebimentoService.ListarFormasRecebimentoDeContaBancaria();
            if (formasRecebimento.Count == 0) return true;

            var formasRecebimentoLoja = formasRecebimento.Select(f => new FormaPagamentoRecebimentoLoja
            {
                LojaId = loja.Id,
                ContaFinanceiraId = contaBancaria.Id,
                FormaPagamentoRecebimentoId = f.Id.Value
            });

            await _formaPagamentoRecebimentoLojaRepository.InsertRange(formasRecebimentoLoja);

            return true;
        }
    }
}
