﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Enums;

namespace Zendar.Data.Mappings.Aplicacao
{
    public class LojaMultaJurosMapping : IEntityTypeConfiguration<LojaMultaJuros>
    {
        public void Configure(EntityTypeBuilder<LojaMultaJuros> builder)
        {
            // Primary Key
            builder.HasKey(p => p.Id);
            builder.Property(p => p.Id).ValueGeneratedOnAdd();

            // Property Entity
            builder.Property(p => p.DataHoraCadastro).HasColumnType("datetime2").IsRequired();
            builder.Property(p => p.DataHoraUltimaAlteracao).HasColumnType("datetime2").IsRequired();

            // Property
            builder.Property(p => p.TipoCobrancaMulta).IsRequired().HasColumnType("varchar(255)").HasConversion<string>().HasDefaultValue(TipoCobrancaMultaJuros.FIXA);
            builder.Property(p => p.TipoValorMulta).IsRequired().HasColumnType("varchar(255)").HasConversion<string>().HasDefaultValue(TipoValor.REAIS);
            builder.Property(p => p.ValorMulta).HasColumnType("decimal(10,2)").HasDefaultValue(0).IsRequired();
            builder.Property(p => p.TipoCobrancaJuros).IsRequired().HasColumnType("varchar(255)").HasConversion<string>().HasDefaultValue(TipoCobrancaMultaJuros.FIXA);
            builder.Property(p => p.TipoValorJuros).IsRequired().HasColumnType("varchar(255)").HasConversion<string>().HasDefaultValue(TipoValor.REAIS);
            builder.Property(p => p.ValorJuros).HasColumnType("decimal(10,2)").HasDefaultValue(0).IsRequired();

            // Relationship
            builder.HasOne(p => p.Loja).WithOne(p => p.LojaMultaJuros).HasForeignKey<LojaMultaJuros>(pt => pt.LojaId).OnDelete(DeleteBehavior.Restrict).IsRequired();
        }
    }
}
