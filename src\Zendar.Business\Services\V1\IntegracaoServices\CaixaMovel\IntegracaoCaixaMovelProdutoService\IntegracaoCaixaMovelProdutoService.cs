﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.IntegracaoServices.CaixaMovel.IntegracaoCaixaMovelService;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;

namespace Zendar.Business.Services.IntegracaoServices.CaixaMovel.IntegracaoCaixaMovelProdutoService
{
    public class IntegracaoCaixaMovelProdutoService : BaseService, IIntegracaoCaixaMovelProdutoService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly IImportacaoCadastroIntegracaoRepository _importacaoCadastroIntegracaoRepository;
        private readonly IIntegracaoCaixaMovelService _integracaoCaixaMovelService;
        private readonly ILojaRepository _lojaRepository;

        public IntegracaoCaixaMovelProdutoService(INotificador notificador,
                                                  IAspNetUserInfo aspNetUserInfo,
                                                  IImportacaoCadastroIntegracaoRepository importacaoCadastroIntegracaoRepository,
                                                  IIntegracaoCaixaMovelService integracaoCaixaMovelService,
                                                  ILojaRepository lojaRepository) : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
			_importacaoCadastroIntegracaoRepository = importacaoCadastroIntegracaoRepository;
            _integracaoCaixaMovelService = integracaoCaixaMovelService;
            _lojaRepository = lojaRepository;
        }

        [Obsolete("Esse método não deve ser usado")]
        public async Task EnviarProdutos(List<Guid> produtos)
        {
            if (produtos == null || produtos.Count() == 0)
            {
                NotificarAviso(ResourceMensagem.IntegracaoCaixaMovelProdutoService_InserirAoMenosUmProduto);
                return;
            }

            var produtosAdicionados = new List<Guid>();

            // Buscar todas as produtos cadastrados na tabela
            var registroProdutos =
                await _importacaoCadastroIntegracaoRepository.ObterRegistrosPorLoja(_aspNetUserInfo.LojaId.Value,
                                                                                    TipoCadastroImportacaoIntegracao.PRODUTO,
                                                                                    IdentificacaoIntegracao.CAIXA_MOVEL);

            if (registroProdutos == null || registroProdutos.Count() == 0)
            {
                produtosAdicionados = produtos;
            }
            else
            {
                // obter as produtos que foram removidos
                var produtosRemovidos = registroProdutos.Where(x => !produtos.Contains(x.Id)).ToList();

                if (produtosRemovidos != null && produtosRemovidos.Count() > 0)
                {
                    // deletar os registros excluídos
                    await _importacaoCadastroIntegracaoRepository.DeleteRange(produtosRemovidos);
                }

                // obter as produtos que foram adicionados
                produtosAdicionados = produtos.Where(x => registroProdutos.Any(y => y.Id == x)).ToList();
            }

            // inserir os registros adicionados
            foreach (var produto in produtosAdicionados)
            {
                await _importacaoCadastroIntegracaoRepository.Insert(new Data.Models.Aplicacao.ImportacaoCadastroIntegracao
                {
                    LojaId = _aspNetUserInfo.LojaId.Value,
                    TipoCadastro = TipoCadastroImportacaoIntegracao.PRODUTO,
                    ProdutoId = produto,
                });
            }

            // Atualizar data de ultima alteração desse tipo de cadastro
            await _integracaoCaixaMovelService.AtualizarDataUltimaAlteracaoRegistro(Data.Enums.TipoCadastroImportacaoIntegracao.PRODUTO, DateTime.UtcNow);
        }

        public async Task EnviarProduto(Guid produto)
        {
            var lojas = await _lojaRepository
                .Where(l => l.Integracoes.Any(i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.CAIXA_MOVEL))
                .Select(l => new Loja
                {
                    Id = l.Id,
                    Integracoes = l.Integracoes
                    .Where(i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.CAIXA_MOVEL)
                    .Select(i => new Data.Models.Aplicacao.Integracao
					{
						Id = i.Id
					})
                    .ToList()
				})
                .ToListAsync();

            foreach(var loja in lojas)
                await _importacaoCadastroIntegracaoRepository.Insert(new Data.Models.Aplicacao.ImportacaoCadastroIntegracao
                {
                    LojaId = loja.Id,
                    TipoCadastro = Data.Enums.TipoCadastroImportacaoIntegracao.PRODUTO,
                    ProdutoId = produto,
                    IntegracaoId = loja.Integracoes.First().Id
                });

            // Atualizar data de ultima alteração desse tipo de cadastro
            //await _integracaoCaixaMovelService.AtualizarDataUltimaAlteracaoRegistro(Data.Enums.TipoCadastroImportacaoIntegracao.PRODUTO, DateTime.UtcNow);
        }

        public void Dispose()
        {
			_importacaoCadastroIntegracaoRepository?.Dispose();
            _integracaoCaixaMovelService?.Dispose();
        }
    }
}
